const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const FuzzyStringMatcher = require('./FuzzyStringMatcher');
const { spawn } = require('child_process');
const ToolRegistry = require('./ToolRegistry');

/**
 * ToolExecutor - 重构后的工具执行器
 *
 * 专门负责：
 * - 执行工具调用（文件读写、目录列表、命令执行）
 * - 工具调用结果管理
 * - 工具调用的错误处理
 * - 与ToolRegistry协作进行工具管理
 */
class ToolExecutor {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      dryRun: false,
      verbose: false,
      commandTimeout: 60000, // 30秒超时
      ...options
    };

    // 使用ToolRegistry管理工具
    this.toolRegistry = new ToolRegistry();

    // 初始化模糊字符串匹配器
    this.fuzzyMatcher = new FuzzyStringMatcher({
      verbose: this.options.verbose,
      ignoreLeadingWhitespace: true,
      ignoreTrailingWhitespace: true,
      normalizeLineEndings: true
    });

    // 执行统计
    this.executionStats = {
      totalCalls: 0,
      successfulCalls: 0,
      failedCalls: 0,
      callsByTool: {}
    };
  }

  /**
   * 获取工具定义（委托给ToolRegistry）
   */
  getTools() {
    return this.toolRegistry.getAllTools();
  }

  /**
   * 获取工具描述（委托给ToolRegistry）
   */
  getToolsDescription() {
    return this.toolRegistry.getToolsDescription();
  }

  /**
   * 执行单个工具调用
   */
  async executeToolCall(toolName, parameters) {
    this.executionStats.totalCalls++;

    if (!this.executionStats.callsByTool[toolName]) {
      this.executionStats.callsByTool[toolName] = 0;
    }
    this.executionStats.callsByTool[toolName]++;

    // 验证工具是否存在
    if (!this.toolRegistry.hasTool(toolName)) {
      const error = `未知的工具: ${toolName}`;
      this.executionStats.failedCalls++;
      return { success: false, error };
    }

    // 验证参数
    const validation = this.toolRegistry.validateToolCall(toolName, parameters);
    if (!validation.valid) {
      this.executionStats.failedCalls++;
      return { success: false, error: validation.error };
    }

    try {
      let result;

      // 根据工具类型执行相应的操作
      switch (toolName) {
        case 'read_file':
          result = await this.readFile(parameters.file_path);
          break;
        case 'write_file':
          result = await this.writeFile(parameters.file_path, parameters.content);
          break;
        case 'str_replace':
          result = await this.stringReplace(
            parameters.file_path,
            parameters.old_string,
            parameters.new_string,
            parameters.expected_replacements
          );
          break;
        case 'list_files':
          result = await this.listFiles(parameters.directory, parameters.pattern);
          break;
        case 'run_command':
          result = await this.runCommand(
            parameters.command,
            parameters.args,
            parameters.working_directory
          );
          break;
        case 'rename_file':
          result = await this.renameFile(
            parameters.source_path,
            parameters.target_path,
            parameters.create_backup
          );
          break;
        default:
          throw new Error(`工具 ${toolName} 的执行器未实现`);
      }

      if (result.success) {
        this.executionStats.successfulCalls++;
      } else {
        this.executionStats.failedCalls++;
      }

      return result;
    } catch (error) {
      this.executionStats.failedCalls++;
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 批量执行工具调用
   */
  async executeToolCalls(toolCalls) {
    if (!Array.isArray(toolCalls)) {
      return { success: false, error: '工具调用必须是数组格式' };
    }

    const results = {};
    const errors = [];

    for (const toolCall of toolCalls) {
      const { name, parameters } = toolCall;

      if (this.options.verbose) {
        console.log(chalk.gray(`🔧 执行工具: ${name}`));
      }

      try {
        const result = await this.executeToolCall(name, parameters);
        const resultKey = this.generateResultKey(toolCall);
        results[resultKey] = result;

        if (!result.success) {
          errors.push(`${name}: ${result.error}`);
        }
      } catch (error) {
        const resultKey = this.generateResultKey(toolCall);
        results[resultKey] = { success: false, error: error.message };
        errors.push(`${name}: ${error.message}`);
      }
    }

    return {
      success: errors.length === 0,
      results,
      errors: errors.length > 0 ? errors : undefined,
      executedCount: toolCalls.length,
      successCount: Object.values(results).filter(r => r.success).length
    };
  }

  /**
   * 生成结果键名
   */
  generateResultKey(toolCall) {
    const { name, parameters } = toolCall;

    switch (name) {
      case 'read_file':
        return `file_${parameters.file_path.replace(/[^a-zA-Z0-9]/g, '_')}`;
      case 'list_files':
        return `list_${parameters.directory.replace(/[^a-zA-Z0-9]/g, '_')}`;
      case 'run_command':
        return `cmd_${parameters.command.split(' ')[0]}`;
      default:
        return `${name}_${Date.now()}`;
    }
  }

  /**
   * 读取文件工具
   */
  async readFile(filePath) {
    try {
      const fullPath = path.resolve(this.projectPath, filePath);

      // 验证文件路径
      const validation = this.validateFilePath(filePath);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }

      if (this.options.verbose) {
        console.log(chalk.gray(`    📖 读取文件: ${filePath}`));
      }

      const content = await fs.readFile(fullPath, 'utf8');

      return {
        success: true,
        content,
        filePath,
        size: content.length
      };
    } catch (error) {
      return {
        success: false,
        error: `无法读取文件 ${filePath}: ${error.message}`,
        filePath
      };
    }
  }

  /**
   * 写入文件工具
   */
  async writeFile(filePath, content) {
    try {
      const fullPath = path.resolve(this.projectPath, filePath);

      // 验证文件路径
      const validation = this.validateFilePath(filePath);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }

      if (this.options.dryRun) {
        console.log(chalk.gray(`    [预览模式] 将写入文件: ${filePath} (${content.length} 字符)`));
        return {
          success: true,
          filePath,
          dryRun: true,
          contentLength: content.length
        };
      }

      if (this.options.verbose) {
        console.log(chalk.gray(`    ✏️  写入文件: ${filePath} (${content.length} 字符)`));
      }

      // 处理 markdown 代码块中的语言标识符
      // 匹配 ```language 格式的代码块
      const codeBlockRegex = /```([a-zA-Z0-9_+-]+)\s*([\s\S]*?)```/;
      const match = content.match(codeBlockRegex);

      if (match) {
        // 如果匹配到了代码块，只写入代码块内的内容
        content = match[2]; // 代码块内容
      }

      // 确保目录存在
      await fs.ensureDir(path.dirname(fullPath));

      // 备份现有文件
      if (await fs.pathExists(fullPath)) {
        const backupPath = `${fullPath}.backup.${Date.now()}`;
        await fs.copy(fullPath, backupPath);

        if (this.options.verbose) {
          console.log(chalk.gray(`    📁 已备份原文件: ${path.basename(backupPath)}`));
        }
      }

      await fs.writeFile(fullPath, content, 'utf8');

      return {
        success: true,
        filePath,
        contentLength: content.length,
        written: true
      };
    } catch (error) {
      return {
        success: false,
        error: `无法写入文件 ${filePath}: ${error.message}`,
        filePath
      };
    }
  }

  /**
   * 字符串替换工具
   * 在文件中精确替换指定的文本内容
   */
  async stringReplace(filePath, oldString, newString, expectedReplacements = 1) {
    try {
      const fullPath = path.resolve(this.projectPath, filePath);

      // 验证文件路径
      const validation = this.validateFilePath(filePath);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }

      if (this.options.verbose) {
        console.log(chalk.gray(`    🔄 字符串替换: ${filePath}`));
        console.log(chalk.gray(`       原文本长度: ${oldString.length} 字符`));
        console.log(chalk.gray(`       新文本长度: ${newString.length} 字符`));
        console.log(chalk.gray(`       期望替换次数: ${expectedReplacements}`));
      }

      // 检查文件是否存在
      if (!await fs.pathExists(fullPath)) {
        return {
          success: false,
          error: `文件不存在: ${filePath}`,
          filePath
        };
      }

      // 读取文件内容
      let currentContent = await fs.readFile(fullPath, 'utf8');

      // 标准化换行符 - 确保所有字符串使用统一的换行符
      currentContent = currentContent.replace(/\r\n/g, '\n');
      const normalizedOldString = oldString.replace(/\r\n/g, '\n');
      const normalizedNewString = newString.replace(/\r\n/g, '\n');

      // 首先尝试精确匹配
      const exactOccurrences = (currentContent.match(new RegExp(this.escapeRegExp(normalizedOldString), 'g')) || []).length;

      if (exactOccurrences === expectedReplacements) {
        // 精确匹配成功，直接替换
        const newContent = currentContent.replaceAll(normalizedOldString, normalizedNewString);

        if (this.options.verbose) {
          console.log(chalk.gray(`    ✅ 精确匹配成功，找到 ${exactOccurrences} 个匹配项`));
        }

        return await this.writeReplacedContent(fullPath, newContent, filePath, exactOccurrences, expectedReplacements);
      }

      // 精确匹配失败，尝试智能匹配
      if (this.options.verbose) {
        console.log(chalk.gray(`    ⚠️  精确匹配失败 (找到 ${exactOccurrences} 个，期望 ${expectedReplacements} 个)`));
        console.log(chalk.gray(`    🔍 尝试智能模糊匹配...`));
      }

      const smartReplaceResult = this.fuzzyMatcher.smartReplace(currentContent, normalizedOldString, normalizedNewString);

      if (smartReplaceResult.success) {
        if (this.options.verbose) {
          console.log(chalk.gray(`    ✅ 智能匹配成功，使用方法: ${smartReplaceResult.method}`));
        }

        return await this.writeReplacedContent(fullPath, smartReplaceResult.result, filePath, 1, expectedReplacements, smartReplaceResult.method);
      }

      // 智能匹配也失败了，返回详细的错误信息和建议
      const suggestions = smartReplaceResult.suggestions || [];
      let errorMessage = `未找到要替换的文本。精确匹配找到 ${exactOccurrences} 个，期望 ${expectedReplacements} 个。`;

      if (suggestions.length > 0) {
        errorMessage += '\n建议尝试以下修复方案：';
        suggestions.forEach((suggestion, index) => {
          errorMessage += `\n${index + 1}. ${suggestion.message}`;
        });
      }

      return {
        success: false,
        error: errorMessage,
        filePath,
        occurrences: exactOccurrences,
        expectedReplacements,
        suggestions
      };


    } catch (error) {
      return {
        success: false,
        error: `字符串替换失败 ${filePath}: ${error.message}`,
        filePath
      };
    }
  }

  /**
   * 写入替换后的内容
   */
  async writeReplacedContent(fullPath, newContent, filePath, occurrences, expectedReplacements, method = 'exact') {
    const originalLength = (await fs.readFile(fullPath, 'utf8')).length;

    if (this.options.dryRun) {
      console.log(chalk.gray(`    [预览模式] 将替换 ${occurrences} 处文本: ${filePath} (方法: ${method})`));
      return {
        success: true,
        filePath,
        dryRun: true,
        occurrences,
        expectedReplacements,
        oldLength: originalLength,
        newLength: newContent.length,
        method
      };
    }

    // 备份现有文件
    const backupPath = `${fullPath}.backup.${Date.now()}`;
    await fs.copy(fullPath, backupPath);

    if (this.options.verbose) {
      console.log(chalk.gray(`    📁 已备份原文件: ${path.basename(backupPath)}`));
    }

    // 写入新内容
    await fs.writeFile(fullPath, newContent, 'utf8');

    if (this.options.verbose) {
      console.log(chalk.gray(`    ✅ 成功替换 ${occurrences} 处文本 (方法: ${method})`));
    }

    return {
      success: true,
      filePath,
      occurrences,
      expectedReplacements,
      oldLength: originalLength,
      newLength: newContent.length,
      replaced: true,
      method
    };
  }

  /**
   * 转义正则表达式特殊字符
   */
  escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * 列出文件工具
   */
  async listFiles(directory, pattern) {
    try {
      const fullPath = path.resolve(this.projectPath, directory);

      // 验证目录路径
      const validation = this.validateFilePath(directory);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }

      if (this.options.verbose) {
        console.log(chalk.gray(`    📂 列出目录: ${directory} (模式: ${pattern || '*'})`));
      }

      if (!await fs.pathExists(fullPath)) {
        return {
          success: false,
          error: `目录不存在: ${directory}`,
          directory
        };
      }

      const stat = await fs.stat(fullPath);
      if (!stat.isDirectory()) {
        return {
          success: false,
          error: `路径不是目录: ${directory}`,
          directory
        };
      }

      let files = await fs.readdir(fullPath);

      // 应用模式过滤
      if (pattern) {
        const regex = new RegExp(pattern.replace(/\*/g, '.*').replace(/\?/g, '.'));
        files = files.filter(file => regex.test(file));
      }

      // 获取文件详细信息
      const fileDetails = await Promise.all(
        files.map(async (file) => {
          try {
            const filePath = path.join(fullPath, file);
            const fileStat = await fs.stat(filePath);
            return {
              name: file,
              path: path.join(directory, file),
              isDirectory: fileStat.isDirectory(),
              size: fileStat.isDirectory() ? null : fileStat.size,
              modified: fileStat.mtime
            };
          } catch (error) {
            return {
              name: file,
              path: path.join(directory, file),
              error: error.message
            };
          }
        })
      );

      return {
        success: true,
        directory,
        pattern,
        files: fileDetails,
        count: fileDetails.length
      };
    } catch (error) {
      return {
        success: false,
        error: `无法列出目录 ${directory}: ${error.message}`,
        directory
      };
    }
  }

  /**
   * 执行命令工具
   */
  async runCommand(command, args, workingDirectory = '') {
    try {
      // 解析命令
      let cmdName, cmdArgs;
      if (Array.isArray(args) && args.length > 0) {
        cmdName = command;
        cmdArgs = args;
      } else {
        const parts = command.split(' ');
        cmdName = parts[0];
        cmdArgs = parts.slice(1);
      }

      // 验证命令安全性
      const validation = this.toolRegistry.validateCommand(cmdName, cmdArgs);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }

      const workDir = workingDirectory
        ? path.resolve(this.projectPath, workingDirectory)
        : this.projectPath;

      if (this.options.verbose) {
        console.log(chalk.gray(`    🔧 执行命令: ${cmdName} ${cmdArgs.join(' ')}`));
        console.log(chalk.gray(`    📁 工作目录: ${workDir}`));
      }

      if (this.options.dryRun) {
        console.log(chalk.gray(`    [预览模式] 将执行命令: ${cmdName} ${cmdArgs.join(' ')}`));
        return {
          success: true,
          command: cmdName,
          args: cmdArgs,
          workingDirectory: workDir,
          dryRun: true
        };
      }

      const options = {
        cwd: workDir,
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: false
      };

      const result = await new Promise((resolve, reject) => {
        const childProcess = spawn(cmdName, cmdArgs, options);

        let stdout = '';
        let stderr = '';

        childProcess.stdout.on('data', data => {
          const chunk = data.toString();
          stdout += chunk;

          if (this.options.verbose) {
            process.stdout.write(chalk.gray('      │ ') + chunk);
          }
        });

        childProcess.stderr.on('data', data => {
          const chunk = data.toString();
          stderr += chunk;

          if (this.options.verbose) {
            process.stderr.write(chalk.gray('      │ ') + chalk.yellow(chunk));
          }
        });

        childProcess.on('close', code => {
          resolve({
            success: code === 0,
            code,
            stdout: stdout.trim(),
            stderr: stderr.trim()
          });
        });

        childProcess.on('error', err => {
          reject(new Error(`命令执行异常: ${err.message}`));
        });

        // 处理超时
        setTimeout(() => {
          if (!childProcess.killed) {
            childProcess.kill('SIGTERM');
            reject(new Error(`命令执行超时 (${this.options.commandTimeout}ms)`));
          }
        }, this.options.commandTimeout);
      });

      if (result.success) {
        if (this.options.verbose) {
          console.log(chalk.green(`      ✅ 命令执行成功 (退出码: ${result.code})`));
        }

        return {
          success: true,
          command: cmdName,
          args: cmdArgs,
          workingDirectory: workDir,
          exitCode: result.code,
          stdout: result.stdout,
          stderr: result.stderr,
          output: result.stdout // 向后兼容
        };
      } else {
        if (this.options.verbose) {
          console.log(chalk.yellow(`      ⚠️  命令执行失败 (退出码: ${result.code})`));
        }

        return {
          success: false,
          command: cmdName,
          args: cmdArgs,
          workingDirectory: workDir,
          exitCode: result.code,
          stdout: result.stdout,
          stderr: result.stderr,
          error: `命令执行失败 (退出码: ${result.code})\n${result.stderr || '无错误信息'}`
        };
      }
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.red(`      ❌ 命令执行异常: ${error.message}`));
      }

      return {
        success: false,
        error: error.message,
        command: command,
        args: args
      };
    }
  }

  /**
   * 验证文件路径
   */
  validateFilePath(filePath) {
    return this.toolRegistry.validateFilePath(filePath);
  }

  /**
   * 格式化上下文文件信息（用于AI）
   */
  formatContextFiles(results) {
    if (!results || Object.keys(results).length === 0) {
      return '无相关上下文文件';
    }

    const contextFiles = {};

    Object.entries(results).forEach(([key, result]) => {
      if (result.success && result.content) {
        // 从结果键名推断文件路径
        let filePath = result.filePath || key.replace(/^file_/, '').replace(/_/g, '/');

        // 截断过长的内容
        const maxLength = 2000;
        let content = result.content;
        if (content.length > maxLength) {
          content = content.substring(0, maxLength) + '\n... (内容已截断)';
        }

        contextFiles[filePath] = content;
      }
    });

    return Object.entries(contextFiles)
      .map(([filePath, content]) => `**${filePath}**:\n\`\`\`\n${content}\n\`\`\``)
      .join('\n\n');
  }

  /**
   * 重命名文件
   */
  async renameFile(sourcePath, targetPath, createBackup = true) {
    try {
      const fullSourcePath = path.resolve(this.projectPath, sourcePath);
      const fullTargetPath = path.resolve(this.projectPath, targetPath);

      // 检查源文件是否存在
      if (!await fs.pathExists(fullSourcePath)) {
        return {
          success: false,
          error: `源文件不存在: ${sourcePath}`
        };
      }

      // 检查目标文件是否已存在
      if (await fs.pathExists(fullTargetPath)) {
        return {
          success: false,
          error: `目标文件已存在: ${targetPath}`
        };
      }

      // 确保目标目录存在
      const targetDir = path.dirname(fullTargetPath);
      await fs.ensureDir(targetDir);

      // 创建备份
      if (createBackup) {
        const backupPath = `${fullSourcePath}.backup.${Date.now()}`;
        await fs.copy(fullSourcePath, backupPath);
        
        if (this.options.verbose) {
          console.log(chalk.gray(`📋 创建备份: ${sourcePath} -> ${path.basename(backupPath)}`));
        }
      }

      // 执行重命名
      if (this.options.dryRun) {
        return {
          success: true,
          message: `[DRY RUN] 将重命名文件: ${sourcePath} -> ${targetPath}`,
          dryRun: true
        };
      }

      await fs.move(fullSourcePath, fullTargetPath);

      if (this.options.verbose) {
        console.log(chalk.green(`✅ 文件重命名成功: ${sourcePath} -> ${targetPath}`));
      }

      return {
        success: true,
        message: `文件重命名成功: ${sourcePath} -> ${targetPath}`,
        source: sourcePath,
        target: targetPath
      };

    } catch (error) {
      console.error(chalk.red(`❌ 重命名文件失败 ${sourcePath} -> ${targetPath}: ${error.message}`));
      return {
        success: false,
        error: error.message
      };
    }
  }

  getToolRegistry() {
    return this.toolRegistry;
  }
}

module.exports = ToolExecutor;
