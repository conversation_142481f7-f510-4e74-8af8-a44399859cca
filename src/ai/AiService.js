const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
require('dotenv').config();
const ai = require('ai');
const aiSdkOpenai = require('@ai-sdk/openai');

const { createUnifiedLogService } = require('../infrastructure/logging');
const { GalaxyAiService } = require('./GalaxyAiService');

let aiAvailable = hasLLMProvider()
function configureLLMProvider() {
  // Check for Galaxy AI configuration first
  if (process.env.GALAXY_SYSTEM_ID && process.env.GALAXY_SYSTEM_SECRET &&
      process.env.GALAXY_APP_ID && process.env.GALAXY_ACCOUNT) {
    try {
      const galaxyService = new GalaxyAiService();
      return {
        fullModel: "galaxy-ai",
        galaxyService,
        providerName: "Galaxy",
        isGalaxy: true
      };
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  Galaxy AI 配置错误: ${error.message}`));
    }
  }

  if (process.env.DEEPSEEK_TOKEN) {
    const openai = aiSdkOpenai.createOpenAI({
      compatibility: "compatible",
      baseURL: "https://api.deepseek.com/v1",
      apiKey: process.env.DEEPSEEK_TOKEN,
    });

    return {
      fullModel: "deepseek-chat",
      openai,
      providerName: "DeepSeek"
    };
  }

  if (process.env.GLM_API_KEY || process.env.GLM_TOKEN) {
    const apiKey = process.env.GLM_API_KEY || process.env.GLM_TOKEN;
    const openai = aiSdkOpenai.createOpenAI({
      compatibility: "compatible",
      baseURL: "https://open.bigmodel.cn/api/paas/v4",
      apiKey: apiKey,
    });

    return {
      fullModel: "glm-4-air",
      openai,
      providerName: "GLM"
    };
  }

  return null;
}

/**
 * Check if any LLM provider is available
 */
function hasLLMProvider() {
  return configureLLMProvider() !== null;
}


/**
 * AI 服务基类
 * 提供通用的 AI 调用功能，集成完整的日志记录和统计分析
 */
class AiService {
  constructor(options = {}) {
    // 只在verbose模式下显示启动信息
    if (options.verbose) {
      console.log(chalk.blue('🚀 启动AI服务 (集成日志功能)...'));
    }

    this.options = {
      maxTokens: options.maxTokens || 4000,
      temperature: options.temperature || 0.0,
      maxRetries: options.maxRetries || 3,
      logDir: options.logDir || path.join(process.cwd(), 'logs'),  // 使用统一日志目录
      enableRealTimeStats: options.enableRealTimeStats !== false,
      enablePerformanceTracking: options.enablePerformanceTracking !== false,
      enableErrorAnalysis: options.enableErrorAnalysis !== false,
      verbose: options.verbose || false,
      ...Object.fromEntries(
        Object.entries(options).filter(([key]) =>
          !['maxTokens', 'temperature', 'maxRetries', 'logDir', 'verbose'].includes(key)
        )
      )
    };

    // 只在verbose模式下显示日志目录
    if (this.options.verbose) {
      console.log(chalk.gray(`🔍 AIService 设置的日志目录: ${this.options.logDir}`));
    }

    // 初始化统一日志服务
    this.loggingService = createUnifiedLogService(this.options.logDir || process.cwd(), {
      logDir: this.options.logDir,
      enableDebugLog: this.options.verbose || false
    });

    // 配置 LLM 提供商
    this.llmConfig = null;
    if (aiAvailable) {
      this.llmConfig = configureLLMProvider();
      if (this.llmConfig) {
        this.enabled = true;
        if (this.options.verbose) {
          console.log(chalk.green(`✅ AI 服务已启用 (${this.llmConfig.providerName})`));
        }
      } else {
        this.enabled = false;
      }
    } else {
      this.enabled = false;
    }

    this.currentSession = null;
    this.stats = {
      attempted: 0,
      success: 0,
      failed: 0,
      skipped: 0
    };
  }

  /**
   * 初始化AI服务
   */
  async initialize() {
    if (this.loggingService && typeof this.loggingService.initialize === 'function') {
      await this.loggingService.initialize();
    }
  }

  /**
   * 检查 AI 服务是否可用
   */
  isEnabled() {
    return this.enabled;
  }

  /**
   * 调用 AI API - 集成完整的日志记录和统计功能
   */
  async callAI(prompt, options = {}) {
    if (!this.enabled) {
      throw new Error('AI 服务未启用或配置不正确');
    }

    const callOptions = {
      maxRetries: this.options.maxRetries || 3,
      ...options
    };

    // 确保 maxRetries 有效
    if (!callOptions.maxRetries || callOptions.maxRetries < 1) {
      callOptions.maxRetries = 3;
    }

    const startTime = Date.now();
    const context = callOptions.context || {};
    const { taskType = 'general', phase = 'main', attemptNumber = 1, fileName = '' } = context;

    // 更新传统统计
    this.stats.attempted++;

    const baseLogData = {
      timestamp: new Date().toISOString(),
      taskType,
      phase,
      attemptNumber,
      fileName,
      provider: this.llmConfig.providerName,
      model: this.llmConfig.fullModel,
      prompt: {
        length: prompt.length,
        preview: prompt
      },
      options: callOptions,
      sessionId: this.currentSession?.sessionId || null
    };

    let lastError = null;
    let result = null;

    // 记录AI调用开始
    if (this.currentSession) {
      this.currentSession.logAICall({
        ...baseLogData,
        status: 'started'
      });
    }

    // 重试循环
    for (let attempt = 1; attempt <= callOptions.maxRetries; attempt++) {
      const attemptStartTime = Date.now();

      try {
        // 只在verbose模式下显示详细的API调用信息
        if (this.options.verbose) {
          console.log(chalk.gray(
            `🤖 调用 ${this.llmConfig.providerName} API (${taskType}/${phase}, 轮次 ${attemptNumber}, 尝试 ${attempt}/${callOptions.maxRetries})...`
          ));
        }

        let text;
        if (this.llmConfig.isGalaxy) {
          const galaxyResult = await this.llmConfig.galaxyService.generateText({
            prompt: prompt,
            maxTokens: this.options.maxTokens,
            temperature: this.options.temperature,
            conversationId: `ai-service-${Date.now()}`
          });
          text = galaxyResult.text;
        } else {
          const result = await ai.generateText({
            model: this.llmConfig.openai(this.llmConfig.fullModel),
            prompt: prompt,
            // maxTokens: this.options.maxTokens,
            temperature: this.options.temperature,
          });
          text = result.text;
        }

        const requestDuration = Date.now() - attemptStartTime;
        const totalDuration = Date.now() - startTime;

        // 处理响应 - 保持原有逻辑
        const currentTaskType = callOptions.context?.taskType || 'general';

        let hasCurrentCodeBlock
        if (currentTaskType === 'file-fix' || currentTaskType === 'error-analysis') {
          result = text.trim();
        } else {
          const codeMatch = text.match(/```[\w]*\n([\s\S]*?)\n```/);
          hasCurrentCodeBlock = !!codeMatch
          result = codeMatch ? codeMatch[1] : text.trim();
        }

        // 更新传统统计
        this.stats.success++;

        // 完整的成功日志数据
        const successLogData = {
          ...baseLogData,
          success: true,
          duration: totalDuration,
          requestDuration,
          attempts: attempt,
          response: {
            length: text.length,
            extractedCode: hasCurrentCodeBlock || currentTaskType === 'file-fix',
            returnFullResponse: currentTaskType === 'file-fix' || currentTaskType === 'error-analysis',
            preview: result.substring(0, 200) + (result.length > 200 ? '...' : '')
          },
          fullResponse: text
        };

        // 记录到统一日志服务
        await this.loggingService.logAIResponse(successLogData);

        // 记录到当前会话
        if (this.currentSession) {
          this.currentSession.logAICall(successLogData);
        }

        // 只在verbose模式下显示成功信息
        if (this.options.verbose) {
          console.log(chalk.green(`✅ AI 响应成功 (${requestDuration}ms)`));
        }
        return result;

      } catch (error) {
        lastError = error;
        const requestDuration = Date.now() - attemptStartTime;

        // 只在verbose模式下显示详细的失败信息
        if (this.options.verbose) {
          console.log(chalk.yellow(
            `⚠️  AI 调用失败 (${taskType}/${phase}, 轮次 ${attemptNumber}, 尝试 ${attempt}/${callOptions.maxRetries}): ${error.message}`
          ));
        }

        // 记录失败尝试
        const failureLogData = {
          ...baseLogData,
          success: false,
          duration: requestDuration,
          attempts: attempt,
          error: {
            message: error.message,
            stack: error.stack,
            type: error.constructor.name
          }
        };

        // 记录到统一日志服务
        await this.loggingService.logAIResponse(failureLogData);

        // 记录到当前会话
        if (this.currentSession) {
          this.currentSession.logAICall(failureLogData);
          this.currentSession.logError('ai-call-failed', error.message, {
            taskType,
            phase,
            attemptNumber,
            attempt
          });
        }

        if (attempt < callOptions.maxRetries) {
          const waitTime = 1000 * attempt;
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }
      }
    }

    // 更新传统统计
    this.stats.failed++;

    // 最终失败日志
    const finalFailureLogData = {
      ...baseLogData,
      success: false,
      duration: Date.now() - startTime,
      attempts: callOptions.maxRetries,
      finalError: {
        message: lastError && lastError.message ? lastError.message : '未知错误',
        stack: lastError && lastError.stack ? lastError.stack : '无堆栈信息'
      }
    };

    await this.loggingService.logAIResponse(finalFailureLogData);

    const errorMessage = lastError && lastError.message ? lastError.message : '未知错误';
    throw new Error(`AI 调用失败，已重试 ${callOptions.maxRetries} 次: ${errorMessage}`);
  }

  getStats() {
    const sessionStatus = this.loggingService.getSessionStatus();
    return {
      // 保持向后兼容的传统统计
      ...this.stats,
      // 增强的统计信息
      enhanced: sessionStatus
    };
  }
}

module.exports = {
  AIService: AiService,
  aiAvailable
};
