const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const LogFormatter = require('./LogFormatter');

/**
 * 日志管理器 - 统一管理AI服务的所有日志功能
 * 负责日志目录管理、日志文件操作和基础日志配置
 */
class LogManager {
  constructor(options = {}) {
    this.options = {
      logDir: options.logDir || path.join(process.cwd(), 'logs'),
      maxLogFiles: options.maxLogFiles || 1000,
      maxLogAge: options.maxLogAge || 7 * 24 * 60 * 60 * 1000, // 7天
      enableDebugLog: options.enableDebugLog || false,
      defaultFormat: options.defaultFormat || 'json',
      supportedFormats: ['json', 'yaml', 'yml', 'markdown', 'md'],
      ...options
    };

    this.logDir = this.options.logDir;
    this.formatter = new LogFormatter(this.options);
    this._ensureLogDir();
  }

  /**
   * 确保日志目录存在
   * @private
   */
  async _ensureLogDir() {
    try {
      await fs.ensureDir(this.logDir);
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  无法创建日志目录 ${this.logDir}: ${error.message}`));
    }
  }

  /**
   * 生成唯一的日志文件名
   * @param {string} type - 日志类型 (ai-prompt, ai-response, migration-error等)
   * @param {string} taskType - 任务类型
   * @param {string} phase - 阶段
   * @param {number} attemptNumber - 尝试次数
   * @param {string} format - 文件格式 (json, yaml, markdown)
   * @returns {string} 日志文件名
   */
  generateLogFileName(type, taskType = '', phase = '', attemptNumber = 1, format = null) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const randomId = Math.random().toString(36).substring(2, 8);

    const parts = [type, taskType, phase, `attempt${attemptNumber}`, timestamp, randomId]
      .filter(part => part && part !== '')
      .join('-');

    const fileFormat = format || this.options.defaultFormat;
    const extension = this.formatter.getFileExtension(fileFormat);

    return `${parts}${extension}`;
  }

  /**
   * 获取日志文件完整路径
   * @param {string} fileName - 日志文件名
   * @returns {string} 完整路径
   */
  getLogFilePath(fileName) {
    return path.join(this.logDir, fileName);
  }

  /**
   * 写入日志文件
   * @param {string} fileName - 文件名
   * @param {Object} logData - 日志数据
   * @param {string} format - 文件格式 (json, yaml, markdown)
   * @returns {Promise<boolean>} 写入是否成功
   */
  async writeLogFile(fileName, logData, format = null) {
    try {
      // 确保日志目录存在
      await this._ensureLogDir();

      const filePath = this.getLogFilePath(fileName);

      // 添加基础元数据
      const enrichedLogData = {
        ...logData,
        timestamp: logData.timestamp || new Date().toISOString(),
        logManager: {
          logDir: this.logDir
        }
      };

      // 确定文件格式
      const fileFormat = format || this._detectFormatFromFileName(fileName) || this.options.defaultFormat;

      // 格式化数据
      const formattedContent = this.formatter.format(enrichedLogData, fileFormat);

      // 写入文件
      await fs.writeFile(filePath, formattedContent, 'utf8');

      if (this.options.enableDebugLog) {
        console.log(chalk.gray(`📝 日志已保存 (${fileFormat}): ${path.basename(fileName)}`));
      }

      return true;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  写入日志文件失败: ${error.message}`));
      return false;
    }
  }

  /**
   * 读取日志文件
   * @param {string} fileName - 文件名
   * @returns {Promise<Object|string|null>} 日志数据
   */
  async readLogFile(fileName) {
    try {
      const filePath = this.getLogFilePath(fileName);
      const format = this._detectFormatFromFileName(fileName);

      if (format === 'json') {
        return await fs.readJson(filePath);
      } else {
        // 对于YAML和Markdown，返回原始文本内容
        return await fs.readFile(filePath, 'utf8');
      }
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  读取日志文件失败: ${fileName} - ${error.message}`));
      return null;
    }
  }

  /**
   * 列出指定类型的日志文件
   * @param {string} type - 日志类型前缀
   * @param {Object} options - 选项
   * @returns {Promise<Array>} 日志文件列表
   */
  async listLogFiles(type = '', options = {}) {
    try {
      await this._ensureLogDir();
      const files = await fs.readdir(this.logDir);

      let logFiles = files
        .filter(file => this._isLogFile(file))
        .filter(file => !type || file.startsWith(type))
        .map(file => ({
          name: file,
          path: path.join(this.logDir, file),
          size: 0,
          modified: null
        }));

      // 获取文件统计信息
      if (options.includeStats) {
        logFiles = await Promise.all(logFiles.map(async (fileInfo) => {
          try {
            const stats = await fs.stat(fileInfo.path);
            return {
              ...fileInfo,
              size: stats.size,
              modified: stats.mtime
            };
          } catch (error) {
            return fileInfo;
          }
        }));
      }

      // 排序
      if (options.sortBy === 'modified') {
        logFiles.sort((a, b) => (b.modified || 0) - (a.modified || 0));
      } else {
        logFiles.sort((a, b) => a.name.localeCompare(b.name));
      }

      // 限制数量
      if (options.limit) {
        logFiles = logFiles.slice(0, options.limit);
      }

      return logFiles;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  列出日志文件失败: ${error.message}`));
      return [];
    }
  }

  /**
   * 清理旧的日志文件
   * @param {Object} options - 清理选项
   * @returns {Promise<Object>} 清理结果
   */
  async cleanupOldLogs(options = {}) {
    const maxAge = options.maxAge || this.options.maxLogAge;
    const maxFiles = options.maxFiles || this.options.maxLogFiles;
    const dryRun = options.dryRun || false;

    try {
      const files = await this.listLogFiles('', { includeStats: true, sortBy: 'modified' });
      const now = new Date().getTime();

      let filesToDelete = [];
      let deletedCount = 0;
      let deletedSize = 0;

      // 按年龄删除
      if (maxAge > 0) {
        const ageFilesToDelete = files.filter(file => {
          if (!file.modified) return false;
          return (now - file.modified.getTime()) > maxAge;
        });
        filesToDelete = filesToDelete.concat(ageFilesToDelete);
      }

      // 按数量删除（保留最新的）
      if (maxFiles > 0 && files.length > maxFiles) {
        const excessFiles = files.slice(maxFiles);
        filesToDelete = filesToDelete.concat(excessFiles);
      }

      // 去除重复
      filesToDelete = [...new Map(filesToDelete.map(file => [file.name, file])).values()];

      // 执行删除
      for (const file of filesToDelete) {
        try {
          if (!dryRun) {
            await fs.remove(file.path);
          }
          deletedCount++;
          deletedSize += file.size || 0;
        } catch (error) {
          console.warn(chalk.yellow(`⚠️  删除日志文件失败: ${file.name} - ${error.message}`));
        }
      }

      const result = {
        totalFiles: files.length,
        deletedCount,
        deletedSize,
        remainingFiles: files.length - deletedCount,
        dryRun
      };

      if (deletedCount > 0) {
        console.log(chalk.blue(
          `🧹 日志清理完成: ${dryRun ? '[预览] ' : ''}删除 ${deletedCount} 个文件，释放 ${Math.round(deletedSize / 1024)}KB 空间`
        ));
      }

      return result;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  清理日志文件失败: ${error.message}`));
      return { error: error.message };
    }
  }

  /**
   * 获取日志目录状态
   * @returns {Promise<Object>} 目录状态信息
   */
  async getLogDirStatus() {
    try {
      await this._ensureLogDir();
      const files = await this.listLogFiles('', { includeStats: true });

      const totalSize = files.reduce((sum, file) => sum + (file.size || 0), 0);
      const typeStats = {};

      files.forEach(file => {
        const type = file.name.split('-')[0] || 'unknown';
        if (!typeStats[type]) {
          typeStats[type] = { count: 0, size: 0 };
        }
        typeStats[type].count++;
        typeStats[type].size += file.size || 0;
      });

      return {
        logDir: this.logDir,
        totalFiles: files.length,
        totalSize,
        typeStats,
        oldestFile: files.length > 0 ? files[files.length - 1] : null,
        newestFile: files.length > 0 ? files[0] : null
      };
    } catch (error) {
      return {
        logDir: this.logDir,
        error: error.message
      };
    }
  }

  /**
   * 搜索日志文件内容
   * @param {string} query - 搜索关键词
   * @param {Object} options - 搜索选项
   * @returns {Promise<Array>} 匹配的日志条目
   */
  async searchLogs(query, options = {}) {
    const { type = '', limit = 50, includeContent = false } = options;

    try {
      const files = await this.listLogFiles(type, { limit: 200 });
      const results = [];

      for (const file of files) {
        if (results.length >= limit) break;

        const logData = await this.readLogFile(file.name);
        if (!logData) continue;

        const contentStr = JSON.stringify(logData).toLowerCase();
        if (contentStr.includes(query.toLowerCase())) {
          results.push({
            fileName: file.name,
            timestamp: logData.timestamp,
            matched: true,
            content: includeContent ? logData : null
          });
        }
      }

      return results;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  搜索日志失败: ${error.message}`));
      return [];
    }
  }

  /**
   * 检查文件是否为日志文件
   * @private
   */
  _isLogFile(fileName) {
    const supportedExtensions = ['.json', '.yml', '.yaml', '.md', '.markdown'];
    return supportedExtensions.some(ext => fileName.endsWith(ext));
  }

  /**
   * 从文件名检测格式
   * @private
   */
  _detectFormatFromFileName(fileName) {
    if (fileName.endsWith('.json')) return 'json';
    if (fileName.endsWith('.yml') || fileName.endsWith('.yaml')) return 'yaml';
    if (fileName.endsWith('.md') || fileName.endsWith('.markdown')) return 'markdown';
    return this.options.defaultFormat;
  }

  /**
   * 创建分类日志目录
   */
  async createCategorizedLogDir(category) {
    const categoryDir = path.join(this.logDir, category);
    try {
      await fs.ensureDir(categoryDir);
      return categoryDir;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  无法创建分类日志目录 ${categoryDir}: ${error.message}`));
      return this.logDir;
    }
  }

  /**
   * 写入AI提示词日志 (YAML格式)
   */
  async writeAIPromptLog(promptData, options = {}) {
    const fileName = this.generateLogFileName(
      'ai-prompt',
      options.taskType || '',
      options.phase || '',
      options.attemptNumber || 1,
      'yaml'
    );

    const logData = {
      type: 'ai-prompt',
      ...promptData,
      metadata: {
        sessionId: options.sessionId,
        attemptNumber: options.attemptNumber,
        phase: options.phase,
        timestamp: new Date().toISOString()
      }
    };

    return await this.writeLogFile(fileName, logData, 'yaml');
  }

  /**
   * 写入迁移错误日志 (Markdown格式)
   */
  async writeMigrationErrorLog(errorData, options = {}) {
    const fileName = this.generateLogFileName(
      'migration-error',
      options.taskType || '',
      options.phase || '',
      options.attemptNumber || 1,
      'markdown'
    );

    const logData = {
      type: 'migration-error',
      ...errorData
    };

    return await this.writeLogFile(fileName, logData, 'markdown');
  }

  /**
   * 写入验证报告 (Markdown格式)
   */
  async writeValidationReport(reportData, options = {}) {
    const fileName = this.generateLogFileName(
      'validation-report',
      options.taskType || '',
      options.phase || '',
      options.attemptNumber || 1,
      'markdown'
    );

    const logData = {
      type: 'validation-report',
      ...reportData
    };

    return await this.writeLogFile(fileName, logData, 'markdown');
  }
}

module.exports = LogManager;
