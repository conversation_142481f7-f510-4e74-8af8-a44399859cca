const LogManager = require('./LogManager')
const SessionLogger = require('./SessionLogger')
const StatisticsCollector = require('./StatisticsCollector')
const LogFormatter = require('./LogFormatter')
const UnifiedLogService = require('./UnifiedLogService')

/**
 * 日志服务入口 - 提供统一的日志服务接口
 * 整合日志管理、会话记录和统计收集功能
 */

// 旧的createLoggingService函数已被弃用，请使用createUnifiedLogService

/**
 * 创建统一日志服务 - 推荐使用
 */
function createUnifiedLogService(projectPath, options = {}) {
	return new UnifiedLogService(projectPath, options)
}

module.exports = {
	LogManager,
	SessionLogger,
	StatisticsCollector,
	LogFormatter,
	UnifiedLogService,
	createUnifiedLogService
}
