const path = require('path');
const chalk = require('chalk');
const LogManager = require('./LogManager');
const SessionLogger = require('./SessionLogger');
const StatisticsCollector = require('./StatisticsCollector');

/**
 * 统一日志服务 - 整合所有日志功能
 * 替代原有的分散日志系统 (ai-logs, migration-logs, validation-reports)
 */
class UnifiedLogService {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      logDir: path.join(projectPath, 'logs'),
      enableDebugLog: false,
      defaultFormat: 'yaml',  // 默认使用 YAML 格式，便于阅读 AI 提示词
      categories: {
        ai: 'ai',           // AI相关日志
        migration: 'migration',  // 迁移日志
        validation: 'validation', // 验证日志
        buildFix: 'build-fix',   // 构建修复日志
        session: 'session'       // 会话日志
      },
      ...options
    };

    // 初始化日志管理器
    this.logManager = new LogManager(this.options);
    
    // 初始化会话日志记录器
    this.sessionLogger = new SessionLogger({
      ...this.options,
      projectPath: this.projectPath
    });
    
    // 初始化统计收集器
    this.statisticsCollector = new StatisticsCollector(this.options);
    
    // 当前会话信息
    this.currentSession = null;
  }

  /**
   * 初始化日志服务
   */
  async initialize() {
    try {
      // 创建分类目录
      for (const [key, category] of Object.entries(this.options.categories)) {
        await this.logManager.createCategorizedLogDir(category);
      }
      
      if (this.options.enableDebugLog) {
        console.log(chalk.green('📁 统一日志服务初始化完成'));
      }
      
      return true;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  日志服务初始化失败: ${error.message}`));
      return false;
    }
  }

  /**
   * 开始新会话
   */
  startSession(sessionOptions = {}) {
    this.currentSession = this.sessionLogger.startSession(sessionOptions);
    return this.currentSession;
  }

  /**
   * 结束当前会话
   */
  async endSession(result = {}) {
    if (this.currentSession) {
      await this.sessionLogger.endSession(result);
      this.currentSession = null;
    }
  }

  /**
   * 记录AI提示词日志 (YAML格式，便于阅读)
   */
  async logAIPrompt(promptData, options = {}) {
    const enrichedOptions = {
      ...options,
      sessionId: this.currentSession?.sessionId,
      category: this.options.categories.ai
    };

    // 使用YAML格式记录AI提示词，便于阅读
    const success = await this.logManager.writeAIPromptLog(promptData, enrichedOptions);
    
    // 同时记录到会话日志
    if (this.currentSession) {
      this.sessionLogger.logAICall({
        type: 'prompt',
        ...promptData,
        format: 'yaml'
      });
    }
    
    return success;
  }

  /**
   * 记录AI响应日志
   */
  async logAIResponse(responseData, options = {}) {
    const fileName = this.logManager.generateLogFileName(
      'ai-response',
      options.taskType || '',
      options.phase || '',
      options.attemptNumber || 1,
      'yaml'  // 使用 YAML 格式
    );

    const logData = {
      type: 'ai-response',
      ...responseData,
      sessionId: this.currentSession?.sessionId
    };

    const success = await this.logManager.writeLogFile(fileName, logData, 'yaml');  // 使用 YAML 格式
    
    // 记录到会话日志
    if (this.currentSession) {
      this.sessionLogger.logAICall({
        type: 'response',
        ...responseData
      });
    }
    
    return success;
  }

  /**
   * 记录迁移错误 (Markdown格式，便于查看)
   */
  async logMigrationError(errorData, options = {}) {
    const enrichedOptions = {
      ...options,
      category: this.options.categories.migration
    };

    const success = await this.logManager.writeMigrationErrorLog(errorData, enrichedOptions);
    
    // 记录到统计
    this.statisticsCollector.recordError(errorData);
    
    return success;
  }

  /**
   * 记录迁移成功
   */
  async logMigrationSuccess(successData, options = {}) {
    const fileName = this.logManager.generateLogFileName(
      'migration-success',
      options.taskType || '',
      options.phase || '',
      options.attemptNumber || 1,
      'yaml'  // 使用 YAML 格式
    );

    const logData = {
      type: 'migration-success',
      ...successData,
      timestamp: new Date().toISOString()
    };

    const success = await this.logManager.writeLogFile(fileName, logData, 'yaml');  // 使用 YAML 格式
    
    // 记录到统计
    this.statisticsCollector.recordSuccess(successData);
    
    return success;
  }

  /**
   * 记录验证报告 (Markdown格式，便于查看)
   */
  async logValidationReport(reportData, options = {}) {
    const enrichedOptions = {
      ...options,
      category: this.options.categories.validation
    };

    return await this.logManager.writeValidationReport(reportData, enrichedOptions);
  }

  /**
   * 记录构建修复日志
   */
  async logBuildFix(fixData, options = {}) {
    const fileName = this.logManager.generateLogFileName(
      'build-fix',
      options.taskType || '',
      options.phase || '',
      options.attemptNumber || 1,
      'yaml'  // 使用 YAML 格式
    );

    const logData = {
      type: 'build-fix',
      ...fixData,
      sessionId: this.currentSession?.sessionId,
      timestamp: new Date().toISOString()
    };

    return await this.logManager.writeLogFile(fileName, logData, 'yaml');  // 使用 YAML 格式
  }

  /**
   * 搜索日志
   */
  async searchLogs(query, options = {}) {
    return await this.logManager.searchLogs(query, options);
  }

  /**
   * 生成综合报告
   */
  async generateComprehensiveReport() {
    const stats = this.statisticsCollector.generateDetailedReport();
    const logStatus = await this.logManager.getLogDirStatus();
    const sessionStatus = this.sessionLogger.getSessionStatus();

    const report = {
      ...stats,
      logDirectory: logStatus,
      currentSession: sessionStatus,
      generatedAt: new Date().toISOString()
    };

    const reportFileName = `comprehensive-report-${new Date().toISOString().split('T')[0]}.md`;
    await this.logManager.writeLogFile(reportFileName, report, 'markdown');

    return {
      report,
      filePath: this.logManager.getLogFilePath(reportFileName)
    };
  }

  /**
   * 清理旧日志
   */
  async cleanupOldLogs(options = {}) {
    return await this.logManager.cleanupOldLogs(options);
  }

  /**
   * 获取日志目录状态
   */
  async getLogStatus() {
    return await this.logManager.getLogDirStatus();
  }

  /**
   * 列出指定类型的日志文件
   */
  async listLogs(type = '', options = {}) {
    return await this.logManager.listLogFiles(type, options);
  }

  /**
   * 获取当前会话状态
   */
  getSessionStatus() {
    return this.sessionLogger.getSessionStatus();
  }
}

module.exports = UnifiedLogService;
