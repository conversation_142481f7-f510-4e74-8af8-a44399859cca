---
# Migration Configuration
source_package: vue-uuid
target_package: vue3-uuid
migration_type: package_replacement
difficulty: easy
vue2_support: false
vue3_support: true

# Package Information
source:
  name: vue-uuid
  github: https://github.com/VitorLuizC/vue-uuid
  npm: https://www.npmjs.com/package/vue-uuid
  description: Vue 2 UUID generation plugin

target:
  name: vue3-uuid
  github: https://github.com/3vilArthas/vue3-uuid
  npm: https://www.npmjs.com/package/vue3-uuid
  description: Vue 3 compatible UUID generation plugin

# Migration Steps
install_commands:
  remove: npm uninstall vue-uuid
  add: npm install vue3-uuid

# Code Examples
vue2_example: |
  <template>
    <div>
      <p>UUID: {{ generatedUuid }}</p>
      <button @click="generateNew">Generate New</button>
    </div>
  </template>

  <script>
  export default {
    data() {
      return {
        generatedUuid: this.$uuid.v4()
      }
    },
    methods: {
      generateNew() {
        this.generatedUuid = this.$uuid.v4()
      }
    }
  }
  </script>

vue3_example: |
  <template>
    <div>
      <p>UUID: {{ generatedUuid }}</p>
      <button @click="generateNew">Generate New</button>
    </div>
  </template>

  <script setup>
  import { ref } from 'vue'
  import { uuid } from 'vue3-uuid'

  const generatedUuid = ref(uuid.v4())

  const generateNew = () => {
    generatedUuid.value = uuid.v4()
  }
  </script>

# API Changes
api_changes:
  - type: import_method
    from: "Global injection via this.$uuid"
    to: "Direct import { uuid } from 'vue3-uuid'"
    description: "Composition API approach with direct imports"
  - type: registration
    from: "Vue.use(UUID) in main.js"
    to: "Optional app.use(UUID) or direct import"
    description: "Plugin registration optional with direct imports"
  - type: access_method
    from: "this.$uuid.v4()"
    to: "uuid.v4()"
    description: "Direct function calls instead of instance property"

# Breaking Changes
breaking_changes:
  - Global injection pattern changed to direct imports
  - Composition API recommended over Options API
  - Plugin registration optional when using direct imports

# UUID Methods Available
uuid_methods:
  - v1: "Time-based UUID"
  - v3: "Namespace and name-based UUID (MD5)"
  - v4: "Random UUID (most common)"
  - v5: "Namespace and name-based UUID (SHA-1)"
---

# Vue UUID Migration Guide

This document provides AI-readable migration instructions for transitioning from `vue-uuid` (Vue 2) to `vue3-uuid` (Vue 3).

## Quick Migration Summary

- **Package Change**: `vue-uuid` → `vue3-uuid`
- **Usage Pattern**: Global injection → Direct imports (recommended)
- **API Access**: `this.$uuid` → `uuid` (imported)
- **Complexity**: Easy - minimal changes required
- **Benefit**: Better tree-shaking and type safety

## Automated Migration Steps

1. **Package Update**:
   ```bash
   npm uninstall vue-uuid
   npm install vue3-uuid
   ```

2. **Import Update** (Recommended approach):
   - From: Global injection `this.$uuid.v4()`
   - To: Direct import `import { uuid } from 'vue3-uuid'`

3. **Usage Update**:
   - From: `this.$uuid.v4()`
   - To: `uuid.v4()`

## Migration Approaches

### Approach 1: Direct Import (Recommended)
```javascript
// Replace this.$uuid usage with direct imports
import { uuid } from 'vue3-uuid'
const newId = uuid.v4()
```

### Approach 2: Global Plugin (Compatibility)
```javascript
// main.js
import { createApp } from 'vue'
import UUID from 'vue3-uuid'

const app = createApp(App)
app.use(UUID)
// Still accessible via this.$uuid in Options API
```

## UUID Methods Available

All UUID versions supported:
- `uuid.v1()` - Time-based UUID
- `uuid.v3(name, namespace)` - Namespace-based (MD5)
- `uuid.v4()` - Random UUID (most common)
- `uuid.v5(name, namespace)` - Namespace-based (SHA-1)

## Validation Points

- ✅ Package installed correctly
- ✅ Import statements updated
- ✅ UUID generation works
- ✅ All UUID versions accessible
- ✅ No global dependency if using direct imports

## Common Issues

- **UUID not defined**: Check import statement
- **this.$uuid undefined**: Either use direct import or register plugin globally
- **Tree-shaking not working**: Use direct imports instead of global registration

## Migration Benefits

- Better tree-shaking with direct imports
- Improved type safety
- Clearer dependency tracking
- Vue 3 Composition API compatibility
- Optional global registration

## Additional Resources

- [Source Package (vue-uuid)](https://github.com/VitorLuizC/vue-uuid)
- [Target Package (vue3-uuid)](https://github.com/3vilArthas/vue3-uuid)
- [NPM Package](https://www.npmjs.com/package/vue3-uuid)
