---
# Migration Configuration
source_package: vue-count-to
target_package: vue3-count-to
migration_type: package_replacement
difficulty: easy
vue2_support: false
vue3_support: true

# Package Information
source:
  name: vue-count-to
  github: https://github.com/PanJiaChen/vue-countTo
  npm: https://www.npmjs.com/package/vue-count-to
  description: A Vue 2 component for animated counting

target:
  name: vue3-count-to
  github: https://github.com/xiaofan9/vue-count-to
  npm: https://www.npmjs.com/package/vue3-count-to
  description: Vue 3 compatible version of vue-count-to

# Migration Steps
install_commands:
  remove: npm uninstall vue-count-to
  add: npm install vue3-count-to

# Code Examples
vue2_example: |
  <template>
    <countTo ref="counter" :startVal='startVal' :endVal='endVal' :duration='3000' @mountedCallback="onMounted"></countTo>
  </template>

  <script>
  import countTo from 'vue-count-to'

  export default {
    components: { countTo },
    data() {
      return {
        startVal: 0,
        endVal: 2023
      }
    },
    methods: {
      onMounted() {
        console.log('Component mounted!')
      }
    }
  }
  </script>

vue3_example: |
  <template>
    <count-to ref="counter" :start-val="startVal" :end-val="endVal" :duration="3000" @mounted="onMounted"></count-to>
  </template>

  <script setup>
  import { ref } from 'vue'
  import { CountTo } from 'vue3-count-to'

  const startVal = ref(0)
  const endVal = ref(2023)

  const onMounted = () => {
    console.log('Component has been mounted!')
  }
  </script>

# API Changes
api_changes:
  - type: event_name
    from: mountedCallback
    to: mounted
    description: Event name changed to follow Vue 3 conventions
  - type: prop_format
    from: startVal
    to: start-val
    description: Props can use kebab-case format
  - type: import_method
    from: "import countTo from 'vue-count-to'"
    to: "import { CountTo } from 'vue3-count-to'"
    description: Named import instead of default import

# Breaking Changes
breaking_changes:
  - Event names follow Vue 3 conventions (kebab-case recommended)
  - Import method changed from default to named import
  - Component registration method updated for Vue 3

# Compatibility Notes
compatibility:
  - Most props remain the same (startVal/start-val, endVal/end-val, duration, etc.)
  - Methods like start(), pause(), reset() still available via ref
  - API is highly compatible with minimal changes needed
---

# Vue Count-To Migration Guide

This document provides AI-readable migration instructions for transitioning from `vue-count-to` (Vue 2) to `vue3-count-to` (Vue 3).

## Quick Migration Summary

- **Package Change**: `vue-count-to` → `vue3-count-to`
- **Import Change**: Default import → Named import
- **Event Change**: `@mountedCallback` → `@mounted`
- **Prop Format**: Support both camelCase and kebab-case
- **Compatibility**: High - most APIs remain the same

## Automated Migration Steps

1. **Package Update**:
   ```bash
   npm uninstall vue-count-to
   npm install vue3-count-to
   ```

2. **Import Update**:
   - From: `import countTo from 'vue-count-to'`
   - To: `import { CountTo } from 'vue3-count-to'`

3. **Event Name Update**:
   - From: `@mountedCallback`
   - To: `@mounted`

4. **Component Registration**:
   - Update component registration for Vue 3 compatibility
   - Use named import instead of default import

## Validation Points

- ✅ Component renders correctly
- ✅ Animation works as expected
- ✅ Event handlers fire properly
- ✅ Ref methods (start, pause, reset) function
- ✅ All props are recognized

## Common Issues

- **Event not firing**: Check event name changed from `mountedCallback` to `mounted`
- **Component not found**: Ensure using named import `{ CountTo }`
- **Props not working**: Both camelCase and kebab-case supported

## Additional Resources

- [Source Package (vue-count-to)](https://github.com/PanJiaChen/vue-countTo)
- [Target Package (vue3-count-to)](https://github.com/xiaofan9/vue-count-to)
- [NPM Package](https://www.npmjs.com/package/vue3-count-to)