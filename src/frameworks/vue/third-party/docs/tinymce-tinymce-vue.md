---
# Migration Configuration
source_package: "@tinymce/tinymce-vue"
target_package: "@tinymce/tinymce-vue"
migration_type: version_upgrade
difficulty: easy
vue2_support: true
vue3_support: true

# Package Information
source:
  name: "@tinymce/tinymce-vue"
  version: "^3.x"
  github: https://github.com/tinymce/tinymce-vue
  npm: https://www.npmjs.com/package/@tinymce/tinymce-vue
  description: "TinyMCE Vue 2 integration (v3.x)"

target:
  name: "@tinymce/tinymce-vue"
  version: "^4.x or ^5.x"
  github: https://github.com/tinymce/tinymce-vue
  npm: https://www.npmjs.com/package/@tinymce/tinymce-vue
  description: "TinyMCE Vue 3 integration (v4.x+)"

# Migration Steps
install_commands:
  remove: npm uninstall @tinymce/tinymce-vue
  add: npm install "@tinymce/tinymce-vue@^4"

# Code Examples
vue2_example: |
  <template>
    <div id="app">
      <editor
        v-model="editorValue"
        :api-key="apiKey"
        :init="init"
      />
    </div>
  </template>

  <script>
  import Editor from '@tinymce/tinymce-vue'

  export default {
    name: 'App',
    components: {
      'editor': Editor
    },
    data() {
      return {
        apiKey: 'YOUR_API_KEY',
        editorValue: '<p>Initial content</p>',
        init: {
          height: 500,
          menubar: true,
          plugins: ['advlist', 'autolink', 'lists', 'link'],
          toolbar: 'undo redo | formatselect | bold italic'
        }
      }
    }
  }
  </script>

vue3_example: |
  <template>
    <main>
      <editor
        v-model="editorValue"
        :api-key="apiKey"
        :init="init"
      />
    </main>
  </template>

  <script setup>
  import { ref } from 'vue'
  import Editor from '@tinymce/tinymce-vue'

  const apiKey = ref('YOUR_API_KEY')
  const editorValue = ref('<p>Initial content for Vue 3</p>')

  const init = {
    height: 500,
    menubar: true,
    plugins: ['advlist', 'autolink', 'lists', 'link'],
    toolbar: 'undo redo | formatselect | bold italic'
  }
  </script>

# API Changes
api_changes:
  - type: version_requirement
    from: "v3.x for Vue 2"
    to: "v4.x+ for Vue 3"
    description: "Different major versions for different Vue versions"
  - type: component_usage
    from: "Options API with data() and components"
    to: "Composition API with ref() and script setup"
    description: "Modern Vue 3 patterns recommended"
  - type: v_model
    from: "v-model works the same"
    to: "v-model works the same"
    description: "No breaking changes in v-model usage"

# Breaking Changes
breaking_changes:
  - Package version must be updated to v4.x+ for Vue 3
  - Component registration syntax changes with script setup
  - Data reactivity uses ref() instead of data()

# Compatibility Notes
compatibility:
  - v-model binding remains identical
  - init configuration object unchanged
  - All TinyMCE features and plugins work the same
  - API key usage identical
  - Event handling compatible
---

# TinyMCE Vue Migration Guide

This document provides AI-readable migration instructions for transitioning from `@tinymce/tinymce-vue` v3.x (Vue 2) to v4.x+ (Vue 3).

## Quick Migration Summary

- **Package Version**: v3.x → v4.x+ (same package, different version)
- **Vue Support**: Vue 2 → Vue 3 compatibility
- **API Compatibility**: High - v-model and init config unchanged
- **Complexity**: Easy - mainly version update and Vue 3 patterns
- **Breaking Changes**: Minimal - mostly Vue 3 syntax adoption

## Automated Migration Steps

1. **Package Update**:
   ```bash
   npm uninstall @tinymce/tinymce-vue
   npm install "@tinymce/tinymce-vue@^4"
   ```

2. **Component Usage Update**:
   - From: Options API with `data()` and `components`
   - To: Composition API with `ref()` and `<script setup>`

3. **Data Reactivity Update**:
   - From: `data() { return { editorValue: '...' } }`
   - To: `const editorValue = ref('...')`

## Version Mapping

| Vue Version | Package Version | Status |
|-------------|----------------|---------|
| Vue 2.x | `@tinymce/tinymce-vue@^3` | Legacy |
| Vue 3.x | `@tinymce/tinymce-vue@^4` | Current |
| Vue 3.x | `@tinymce/tinymce-vue@^5` | Latest |

## API Compatibility

### Unchanged Features
- ✅ `v-model` binding works identically
- ✅ `:api-key` prop unchanged
- ✅ `:init` configuration object identical
- ✅ All TinyMCE plugins and features work the same
- ✅ Event handling compatible
- ✅ Editor methods and API access unchanged

### Vue 3 Adaptations
- Component registration via `<script setup>`
- Reactive data using `ref()` instead of `data()`
- Composition API patterns recommended

## Validation Points

- ✅ Package version updated to v4.x+
- ✅ Editor renders correctly
- ✅ v-model binding works
- ✅ TinyMCE configuration applied
- ✅ All plugins load properly
- ✅ API key authentication works
- ✅ Content editing functions normally

## Common Issues

- **Editor not loading**: Check package version is v4.x+ for Vue 3
- **v-model not working**: Ensure using `ref()` for reactive data
- **Component not found**: Verify import statement and script setup usage
- **Configuration not applied**: Check init object structure (should be unchanged)

## Migration Benefits

- Vue 3 compatibility and performance
- Modern Composition API patterns
- Better TypeScript support
- Continued TinyMCE feature parity
- Official package maintenance

## Additional Resources

- [TinyMCE Vue Integration](https://github.com/tinymce/tinymce-vue)
- [NPM Package](https://www.npmjs.com/package/@tinymce/tinymce-vue)
- [TinyMCE Documentation](https://www.tiny.cloud/docs/)
- [Vue 3 Migration Guide](https://v3-migration.vuejs.org/)