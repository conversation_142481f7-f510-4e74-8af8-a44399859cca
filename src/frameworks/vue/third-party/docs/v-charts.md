---
# Migration Configuration
source_package: v-charts
target_package: vue-echarts
migration_type: data_format_change
difficulty: hard
vue2_support: false
vue3_support: true

# Package Information
source:
  name: v-charts
  github: https://github.com/ElemeFE/v-charts
  npm: https://www.npmjs.com/package/v-charts
  description: Vue 2 chart components based on ECharts with simplified data format

target:
  name: vue-echarts
  github: https://github.com/ecomfe/vue-echarts
  npm: https://www.npmjs.com/package/vue-echarts
  description: Vue 3 and Vue 2 compatible ECharts component with native ECharts options
  website: https://vue-echarts.dev/

# Migration Steps
install_commands:
  remove: npm uninstall v-charts
  add: npm install echarts vue-echarts

# Code Examples
vue2_example: |
  <template>
    <ve-line :data="chartData" />
  </template>

  <script>
  import VeLine from 'v-charts/lib/line.common'

  export default {
    components: { VeLine },
    data() {
      return {
        chartData: {
          columns: ['日期', '销售额-A', '销售额-B'],
          rows: [
            { '日期': '周一', '销售额-A': 120, '销售额-B': 80 },
            { '日期': '周二', '销售额-A': 200, '销售额-B': 130 },
            { '日期': '周三', '销售额-A': 150, '销售额-B': 110 }
          ]
        }
      }
    }
  }
  </script>

vue3_example: |
  <template>
    <v-chart class="chart" :option="option" />
  </template>

  <script setup>
  import { use } from 'echarts/core'
  import { CanvasRenderer } from 'echarts/renderers'
  import { LineChart } from 'echarts/charts'
  import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components'
  import VChart from 'vue-echarts'
  import { ref } from 'vue'

  use([CanvasRenderer, LineChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent])

  // Transform v-charts data to ECharts option
  const chartData = {
    columns: ['日期', '销售额-A', '销售额-B'],
    rows: [
      { '日期': '周一', '销售额-A': 120, '销售额-B': 80 },
      { '日期': '周二', '销售额-A': 200, '销售额-B': 130 },
      { '日期': '周三', '销售额-A': 150, '销售额-B': 110 }
    ]
  }

  const dimension = chartData.columns[0]
  const metrics = chartData.columns.slice(1)

  const option = ref({
    tooltip: { trigger: 'axis' },
    legend: { data: metrics },
    xAxis: {
      type: 'category',
      data: chartData.rows.map(row => row[dimension])
    },
    yAxis: { type: 'value' },
    series: metrics.map(metric => ({
      name: metric,
      type: 'line',
      data: chartData.rows.map(row => row[metric])
    }))
  })
  </script>

# Data Transformation
data_transformation:
  description: "Convert v-charts simplified data format to ECharts native option format"
  input_format: "{ columns: [], rows: [] }"
  output_format: "ECharts option object with series, xAxis, yAxis, etc."
  transformation_logic: |
    1. Extract dimension (first column) for xAxis data
    2. Extract metrics (remaining columns) for series
    3. Map rows data to series data arrays
    4. Configure ECharts components (tooltip, legend, etc.)

# API Changes
api_changes:
  - type: component_name
    from: "<ve-line>, <ve-bar>, <ve-pie>, etc."
    to: "<v-chart>"
    description: "Single unified component instead of chart-specific components"
  - type: data_prop
    from: ":data='chartData'"
    to: ":option='option'"
    description: "Data format completely changed from simplified to native ECharts"
  - type: import_method
    from: "import VeLine from 'v-charts/lib/line.common'"
    to: "import VChart from 'vue-echarts'"
    description: "Single component import with ECharts modules"
  - type: echarts_modules
    from: "Automatic inclusion"
    to: "Manual tree-shaking imports"
    description: "Must manually import required ECharts components"

# Breaking Changes
breaking_changes:
  - Complete data format change from columns/rows to ECharts option
  - Component architecture change from chart-specific to unified
  - Manual ECharts module imports required
  - No automatic data processing - manual transformation needed
  - Different event handling and customization approach

# Migration Complexity
complexity_factors:
  - Data format transformation required for every chart
  - ECharts knowledge needed for advanced customization
  - Manual module imports for tree-shaking
  - Different component structure and props
  - Event handling differences
---

# V-Charts Migration Guide

This document provides AI-readable migration instructions for transitioning from `v-charts` (Vue 2) to `vue-echarts` (Vue 3 compatible).

## Quick Migration Summary

- **Package Change**: `v-charts` → `vue-echarts` + `echarts`
- **Data Format**: `{ columns, rows }` → ECharts `option` object
- **Component**: Chart-specific components → Unified `<v-chart>`
- **Complexity**: Hard - complete data transformation required
- **Benefit**: Full ECharts capabilities and flexibility

## Automated Migration Steps

1. **Package Update**:
   ```bash
   npm uninstall v-charts
   npm install echarts vue-echarts
   ```

2. **Import Update**:
   - From: `import VeLine from 'v-charts/lib/line.common'`
   - To: `import VChart from 'vue-echarts'`

3. **ECharts Modules** (Required for tree-shaking):
   ```javascript
   import { use } from 'echarts/core'
   import { CanvasRenderer } from 'echarts/renderers'
   import { LineChart } from 'echarts/charts'
   import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components'

   use([CanvasRenderer, LineChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent])
   ```

4. **Data Transformation** (Critical):
   - Convert `{ columns, rows }` format to ECharts `option`
   - Map first column to xAxis data
   - Map remaining columns to series data

## Data Transformation Algorithm

```javascript
function transformVChartsData(chartData, chartType = 'line') {
  const dimension = chartData.columns[0]
  const metrics = chartData.columns.slice(1)

  return {
    tooltip: { trigger: 'axis' },
    legend: { data: metrics },
    xAxis: {
      type: 'category',
      data: chartData.rows.map(row => row[dimension])
    },
    yAxis: { type: 'value' },
    series: metrics.map(metric => ({
      name: metric,
      type: chartType,
      data: chartData.rows.map(row => row[metric])
    }))
  }
}
```

## Component Mapping

| v-charts | vue-echarts | Chart Type |
|----------|-------------|------------|
| `<ve-line>` | `<v-chart>` | `type: 'line'` |
| `<ve-bar>` | `<v-chart>` | `type: 'bar'` |
| `<ve-pie>` | `<v-chart>` | `type: 'pie'` |
| `<ve-scatter>` | `<v-chart>` | `type: 'scatter'` |

## Validation Points

- ✅ ECharts modules imported correctly
- ✅ Data transformation logic implemented
- ✅ Chart renders with correct data
- ✅ Tooltip and legend work properly
- ✅ Chart type matches original
- ✅ Styling and dimensions preserved

## Common Issues

- **Chart not rendering**: Check ECharts module imports
- **Data not displaying**: Verify data transformation logic
- **Missing features**: Ensure all required ECharts components imported
- **Performance issues**: Use tree-shaking with selective imports

## Migration Benefits

- Full ECharts API access
- Better performance with tree-shaking
- Vue 3 compatibility
- More customization options
- Better TypeScript support
- Active maintenance and updates

## Additional Resources

- [Source Package (v-charts)](https://github.com/ElemeFE/v-charts)
- [Target Package (vue-echarts)](https://github.com/ecomfe/vue-echarts)
- [ECharts Documentation](https://echarts.apache.org/en/option.html)
- [Vue-ECharts Documentation](https://vue-echarts.dev/)
- [NPM Package](https://www.npmjs.com/package/vue-echarts)