---
# Migration Configuration
source_package: vue-splitpane
target_package: splitpanes
migration_type: component_architecture_change
difficulty: medium
vue2_support: false
vue3_support: true

# Package Information
source:
  name: vue-splitpane
  github: https://github.com/PanJiaChen/vue-split-pane
  npm: https://www.npmjs.com/package/vue-splitpane
  description: Vue 2 split pane component

target:
  name: splitpanes
  github: https://github.com/antoniandre/splitpanes
  npm: https://www.npmjs.com/package/splitpanes
  description: Vue 3 and Vue 2 compatible split panes component
  website: https://antoniandre.github.io/splitpanes/

# Migration Steps
install_commands:
  remove: npm uninstall vue-splitpane
  add: npm install splitpanes

# Code Examples
vue2_example: |
  <template>
    <split-pane :min-percent='20' :default-percent='30' split="vertical">
      <template slot="paneL">A</template>
      <template slot="paneR">B</template>
    </split-pane>
  </template>

  <script>
  import splitPane from 'vue-splitpane'

  export default {
    components: { splitPane }
  }
  </script>

vue3_example: |
  <template>
    <splitpanes style="height: 400px">
      <pane min-size="20" size="30">
        <span>A</span>
      </pane>
      <pane>
        <span>B</span>
      </pane>
    </splitpanes>
  </template>

  <script setup>
  import { Splitpanes, Pane } from 'splitpanes'
  import 'splitpanes/dist/splitpanes.css'
  </script>

# API Changes
api_changes:
  - type: component_structure
    from: "Single component with slots"
    to: "Container + Pane components"
    description: "Architecture changed from slot-based to component-based"
  - type: prop_mapping
    from: "min-percent"
    to: "min-size"
    description: "Percentage-based props renamed"
  - type: prop_mapping
    from: "default-percent"
    to: "size"
    description: "Initial size prop renamed"
  - type: slot_removal
    from: "slot='paneL', slot='paneR'"
    to: "Direct content in <pane> components"
    description: "Slots replaced with component content"
  - type: css_import
    from: "No CSS import needed"
    to: "import 'splitpanes/dist/splitpanes.css'"
    description: "CSS must be imported manually"

# Breaking Changes
breaking_changes:
  - Complete component architecture change
  - Slot-based approach replaced with component-based
  - CSS import required
  - Different prop names and structure
  - Event handling differences

# Migration Complexity
complexity_factors:
  - Component structure completely different
  - Props mapping required
  - CSS import needed
  - Event handlers may need updates
  - Nested layouts require restructuring
---

# Vue Split-Pane Migration Guide

This document provides AI-readable migration instructions for transitioning from `vue-splitpane` (Vue 2) to `splitpanes` (Vue 3 compatible).

## Quick Migration Summary

- **Package Change**: `vue-splitpane` → `splitpanes`
- **Architecture Change**: Slot-based → Component-based
- **CSS Import**: Required manual import
- **Prop Mapping**: `min-percent` → `min-size`, `default-percent` → `size`
- **Complexity**: Medium - significant structural changes

## Automated Migration Steps

1. **Package Update**:
   ```bash
   npm uninstall vue-splitpane
   npm install splitpanes
   ```

2. **Import Update**:
   - From: `import splitPane from 'vue-splitpane'`
   - To: `import { Splitpanes, Pane } from 'splitpanes'`

3. **CSS Import** (Required):
   ```javascript
   import 'splitpanes/dist/splitpanes.css'
   ```

4. **Component Structure Update**:
   - Replace `<split-pane>` with `<splitpanes>`
   - Replace slot content with `<pane>` components
   - Update prop names and values

## Prop Mapping Table

| vue-splitpane | splitpanes | Notes |
|---------------|------------|-------|
| `min-percent="20"` | `min-size="20"` | Percentage-based sizing |
| `default-percent="30"` | `size="30"` | Initial size setting |
| `split="vertical"` | (default) | Vertical is default in splitpanes |
| `split="horizontal"` | `horizontal` | Boolean prop |

## Component Structure Transformation

**Before (vue-splitpane)**:
```html
<split-pane :min-percent='20' :default-percent='30'>
  <template slot="paneL">Content A</template>
  <template slot="paneR">Content B</template>
</split-pane>
```

**After (splitpanes)**:
```html
<splitpanes>
  <pane min-size="20" size="30">Content A</pane>
  <pane>Content B</pane>
</splitpanes>
```

## Validation Points

- ✅ CSS imported correctly
- ✅ Component structure updated
- ✅ Props mapped correctly
- ✅ Resize functionality works
- ✅ Event handlers updated if needed
- ✅ Nested layouts restructured

## Common Issues

- **Styling broken**: Ensure CSS import `'splitpanes/dist/splitpanes.css'`
- **Component not found**: Check named imports `{ Splitpanes, Pane }`
- **Layout not working**: Verify component structure transformation
- **Props not recognized**: Check prop name mapping

## Additional Features in splitpanes

- Touch support
- RTL language support
- More events (ready, resize, pane-click, etc.)
- Better nesting support
- Customizable splitter appearance

## Additional Resources

- [Source Package (vue-splitpane)](https://github.com/PanJiaChen/vue-split-pane)
- [Target Package (splitpanes)](https://github.com/antoniandre/splitpanes)
- [Official Documentation](https://antoniandre.github.io/splitpanes/)
- [NPM Package](https://www.npmjs.com/package/splitpanes)