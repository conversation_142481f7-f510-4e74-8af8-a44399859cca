---
# Migration Configuration
source_package: package-name-vue2
target_package: package-name-vue3
migration_type: package_replacement | version_upgrade | component_architecture_change | data_format_change
difficulty: easy | medium | hard
vue2_support: true | false
vue3_support: true | false

# Package Information
source:
  name: source-package-name
  github: https://github.com/owner/source-repo
  npm: https://www.npmjs.com/package/source-package
  description: Brief description of source package
  
target:
  name: target-package-name
  github: https://github.com/owner/target-repo
  npm: https://www.npmjs.com/package/target-package
  description: Brief description of target package
  website: https://optional-website.com (if available)

# Migration Steps
install_commands:
  remove: npm uninstall source-package
  add: npm install target-package

# Code Examples
vue2_example: |
  <template>
    <!-- Vue 2 template example -->
    <source-component v-model="value" :prop="config" @event="handler" />
  </template>
  
  <script>
  import SourceComponent from 'source-package'
  
  export default {
    components: { SourceComponent },
    data() {
      return {
        value: 'initial',
        config: { option: true }
      }
    },
    methods: {
      handler() {
        // Event handler
      }
    }
  }
  </script>

vue3_example: |
  <template>
    <!-- Vue 3 template example -->
    <target-component v-model="value" :prop="config" @event="handler" />
  </template>
  
  <script setup>
  import { ref } from 'vue'
  import TargetComponent from 'target-package'
  
  const value = ref('initial')
  const config = { option: true }
  
  const handler = () => {
    // Event handler
  }
  </script>

# API Changes
api_changes:
  - type: prop_name | event_name | import_method | component_structure
    from: "old API"
    to: "new API"
    description: "Description of the change"
  - type: breaking_change
    from: "old behavior"
    to: "new behavior"
    description: "What changed and why"

# Breaking Changes
breaking_changes:
  - List of breaking changes
  - Each change should be specific
  - Include impact and solution

# Migration Complexity
complexity_factors:
  - Factor 1 that affects migration difficulty
  - Factor 2 that affects migration difficulty
  - Factor 3 that affects migration difficulty

# Data Transformation (if applicable)
data_transformation:
  description: "How data format changes"
  input_format: "Original data structure"
  output_format: "New data structure"
  transformation_logic: |
    Step-by-step transformation process
    1. Extract data from old format
    2. Transform to new format
    3. Validate new structure

# Compatibility Notes
compatibility:
  - What remains the same
  - What works without changes
  - Backward compatibility notes
---

# Package Migration Guide

This document provides AI-readable migration instructions for transitioning from `source-package` (Vue 2) to `target-package` (Vue 3).

## Quick Migration Summary

- **Package Change**: `source-package` → `target-package`
- **Migration Type**: [Type from frontmatter]
- **Difficulty**: [Difficulty from frontmatter]
- **Key Changes**: [List 2-3 most important changes]

## Automated Migration Steps

1. **Package Update**:
   ```bash
   npm uninstall source-package
   npm install target-package
   ```

2. **Import Update**:
   - From: `import Component from 'source-package'`
   - To: `import Component from 'target-package'`

3. **Usage Update**:
   - [Specific changes needed]
   - [API updates required]

## Validation Points

- ✅ Package installed correctly
- ✅ Component renders without errors
- ✅ All props work as expected
- ✅ Events fire correctly
- ✅ Styling preserved
- ✅ Functionality matches original

## Common Issues

- **Issue 1**: Description and solution
- **Issue 2**: Description and solution
- **Issue 3**: Description and solution

## Migration Benefits

- Benefit 1 of using the new package
- Benefit 2 of using the new package
- Benefit 3 of using the new package

## Additional Resources

- [Source Package GitHub](link-from-frontmatter)
- [Target Package GitHub](link-from-frontmatter)
- [NPM Package](link-from-frontmatter)
- [Documentation](link-if-available)
