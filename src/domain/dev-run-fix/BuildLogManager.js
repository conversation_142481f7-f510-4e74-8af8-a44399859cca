const chalk = require('chalk');

/**
 * BuildLogManager - 构建日志管理器
 *
 * 职责：
 * 1. 清理和过滤构建日志
 * 2. 根据 ANSI 颜色代码识别日志重要性
 * 3. 提取关键错误和警告信息
 * 4. 生成简洁的日志摘要
 */
class BuildLogManager {
  constructor(options = {}) {
    this.options = {
      verbose: false,
      showProgress: false,
      showWarnings: true,
      showErrors: true,
      ...options
    };

    // ANSI 颜色代码映射
    this.colorMap = {
      // 错误 - 红色
      '31': 'error',
      '91': 'error',
      // 警告 - 黄色
      '33': 'warning',
      '93': 'warning',
      // 信息 - 蓝色
      '34': 'info',
      '94': 'info',
      // 成功 - 绿色
      '32': 'success',
      '92': 'success',
      // 灰色 - 调试
      '90': 'debug',
      '37': 'debug'
    };

    // 日志级别优先级
    this.logLevels = {
      'error': 4,
      'warning': 3,
      'info': 2,
      'success': 2,
      'debug': 1
    };
  }

  /**
   * 处理原始构建日志
   */
  processRawLog(rawOutput) {
    const lines = rawOutput.split('\n');
    const processedLines = [];
    const summary = {
      errors: [],
      warnings: [],
      info: [],
      progress: null,
      hasCompilationError: false
    };

    for (let line of lines) {
      const processed = this.processLine(line);

      if (processed) {
        processedLines.push(processed);
        this.categorizeLogLine(processed, summary);
      }
    }

    return {
      cleanedOutput: processedLines.map(line => line.cleaned).join('\n'),
      summary: summary,
      importantLines: this.extractImportantLines(processedLines)
    };
  }

  /**
   * 处理单行日志
   */
  processLine(line) {
    if (!line || line.trim() === '') {
      return null;
    }

    // 检测颜色信息（在清理之前）
    const colorInfo = this.extractColorInfo(line);

    // 移除所有 ANSI 转义序列
    let cleaned = line.replace(/\x1b\[[0-9;]*[A-Za-z]/g, '');

    // 移除特殊的终端控制序列
    cleaned = cleaned.replace(/\[2K\[1A\[2K\[G/g, '');
    cleaned = cleaned.replace(/\[2K/g, '');
    cleaned = cleaned.replace(/\[1A/g, '');
    cleaned = cleaned.replace(/\[G/g, '');

    // 如果清理后为空，跳过
    if (!cleaned.trim()) {
      return null;
    }

    // 过滤掉不重要的日志
    if (this.shouldSkipLine(cleaned)) {
      return null;
    }

    return {
      original: line,
      cleaned: cleaned.trim(),
      level: colorInfo.level,
      color: colorInfo.color,
      isImportant: this.isImportantLine(cleaned, colorInfo.level)
    };
  }

  /**
   * 提取 ANSI 颜色信息
   */
  extractColorInfo(line) {
    // 匹配 ANSI 颜色代码，如 \x1b[31m, \x1b[1;33m
    const colorMatch = line.match(/\x1b\[([0-9;]+)m/);

    if (colorMatch) {
      const codes = colorMatch[1].split(';');

      // 查找颜色代码
      for (let code of codes) {
        if (this.colorMap[code]) {
          return {
            level: this.colorMap[code],
            color: code
          };
        }
      }
    }

    // 根据内容推断级别
    return {
      level: this.inferLogLevel(line),
      color: null
    };
  }

  /**
   * 根据内容推断日志级别
   */
  inferLogLevel(line) {
    const lowerLine = line.toLowerCase();

    if (lowerLine.includes('error') || lowerLine.includes('failed')) {
      return 'error';
    }

    if (lowerLine.includes('warning') || lowerLine.includes('warn') ||
        lowerLine.includes('deprecated')) {
      return 'warning';
    }

    if (lowerLine.includes('info') || lowerLine.includes('starting')) {
      return 'info';
    }

    if (lowerLine.includes('compiled') || lowerLine.includes('success')) {
      return 'success';
    }

    return 'debug';
  }

  /**
   * 判断是否应该跳过这行日志
   */
  shouldSkipLine(cleaned) {
    const skipPatterns = [
      // Webpack 进度信息
      /^\[\d+%\]\s+(setup|building|sealing|emitting)/,
      // 重复的 "Build finished" 信息
      /^Build finished at \d+:\d+:\d+ by/,
      // 空的进度行
      /^\[\d+%\]\s*$/,
      // Webpack 插件加载信息
      /^\[\d+%\]\s+setup \(compilation [^)]+Plugin\)/,
      // 模块导入信息（太详细）
      /^\[\d+%\]\s+building \(import loader/,
      // 调试器信息
      /^Debugger (listening|attached)/,
      /^For help, see: https:\/\/nodejs\.org/
    ];

    return skipPatterns.some(pattern => pattern.test(cleaned));
  }

  /**
   * 判断是否为重要日志行
   */
  isImportantLine(cleaned, level) {
    // 错误和警告总是重要的
    if (level === 'error' || level === 'warning') {
      return true;
    }

    // 关键信息
    const importantPatterns = [
      /Starting development server/,
      /Local:\s+http/,
      /Network:\s+http/,
      /App running at/,
      /Compiled (successfully|with \d+ error)/,
      /Failed to compile/,
      /Configuration for rule .* is invalid/,
      /Module not found/,
      /::v-deep usage as a combinator has been deprecated/
    ];

    return importantPatterns.some(pattern => pattern.test(cleaned));
  }

  /**
   * 将日志行分类到摘要中
   */
  categorizeLogLine(processedLine, summary) {
    const { cleaned, level } = processedLine;

    switch (level) {
      case 'error':
        summary.errors.push(cleaned);
        if (cleaned.includes('Failed to compile')) {
          summary.hasCompilationError = true;
        }
        break;
      case 'warning':
        summary.warnings.push(cleaned);
        break;
      case 'info':
      case 'success':
        summary.info.push(cleaned);
        break;
    }

    // 提取进度信息
    const progressMatch = cleaned.match(/\[(\d+)%\]/);
    if (progressMatch) {
      summary.progress = parseInt(progressMatch[1]);
    }
  }

  /**
   * 提取重要的日志行
   */
  extractImportantLines(processedLines) {
    return processedLines
      .filter(line => line.isImportant)
      .map(line => line.cleaned);
  }

  /**
   * 生成简洁的日志摘要
   */
  generateSummary(summary) {
    const parts = [];

    if (summary.errors.length > 0) {
      parts.push(chalk.red(`❌ ${summary.errors.length} 个错误`));
    }

    if (summary.warnings.length > 0) {
      parts.push(chalk.yellow(`⚠️  ${summary.warnings.length} 个警告`));
    }

    if (summary.progress !== null) {
      parts.push(chalk.gray(`📊 进度: ${summary.progress}%`));
    }

    return parts.join(' | ');
  }
}

module.exports = BuildLogManager;
