const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const {  exec } = require('child_process');
const { promisify } = require('util');
const { browserFactory } = require('../../../infrastructure/browser');

const execAsync = promisify(exec);

/**
 * BrowserDetector - 浏览器检测和管理工具
 *
 * 功能：
 * 1. 检测系统中可用的浏览器
 * 2. 自动下载和安装 Puppeteer 浏览器
 * 3. 提供浏览器兼容性检测
 * 4. 支持多种浏览器引擎
 */
class BrowserDetector {
  constructor(options = {}) {
    this.options = {
      verbose: false,
      preferredBrowsers: ['chrome', 'chromium', 'edge'],
      downloadTimeout: 300000, // 5分钟下载超时
      ...options
    };

    this.detectedBrowsers = [];
    this.selectedBrowser = null;
  }

  /**
   * 检测所有可用的浏览器
   */
  async detectAvailableBrowsers() {
    console.log(chalk.blue('🔍 检测可用浏览器...'));

    const platform = process.platform;
    this.detectedBrowsers = [];

    // 检测 Puppeteer 浏览器
    await this.detectPuppeteerBrowsers();

    // 检测系统浏览器
    await this.detectSystemBrowsers(platform);

    if (this.options.verbose) {
      this.printDetectedBrowsers();
    }

    return this.detectedBrowsers;
  }

  /**
   * 检测所有浏览器自动化工具的浏览器
   */
  async detectPuppeteerBrowsers() {
    // 检测 Puppeteer 浏览器
    await this.detectBrowsersForAdapter('puppeteer');

    // 检测 Playwright 浏览器
    await this.detectBrowsersForAdapter('playwright-chromium');
    await this.detectBrowsersForAdapter('playwright-firefox');
    await this.detectBrowsersForAdapter('playwright-webkit');
  }

  /**
   * 检测指定适配器的浏览器
   */
  async detectBrowsersForAdapter(adapterType) {
    try {
      const adapter = browserFactory.createBrowserAutomation(adapterType);
      const browsers = await adapter.detectBrowsers();

      this.detectedBrowsers.push(...browsers);

      if (this.options.verbose && browsers.length > 0) {
        console.log(chalk.green(`✓ 找到 ${browsers.length} 个 ${adapterType} 浏览器`));
      }
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  ${adapterType} 未安装或配置错误: ${error.message}`));
      }
    }
  }

  /**
   * 检测系统浏览器
   */
  async detectSystemBrowsers(platform) {
    const browserConfigs = this.getBrowserConfigs(platform);

    for (const config of browserConfigs) {
      try {
        const isAvailable = await this.checkBrowserAvailability(config);
        if (isAvailable) {
          this.detectedBrowsers.push({
            ...config,
            available: true
          });
        }
      } catch (error) {
        if (this.options.verbose) {
          console.log(chalk.yellow(`⚠️  检测 ${config.name} 失败: ${error.message}`));
        }
      }
    }
  }

  /**
   * 获取不同平台的浏览器配置
   */
  getBrowserConfigs(platform) {
    const configs = {
      darwin: [
        {
          name: 'Google Chrome',
          type: 'chrome',
          executablePath: '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
          command: 'google-chrome'
        },
        {
          name: 'Chromium',
          type: 'chromium',
          executablePath: '/Applications/Chromium.app/Contents/MacOS/Chromium',
          command: 'chromium'
        },
        {
          name: 'Microsoft Edge',
          type: 'edge',
          executablePath: '/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge',
          command: 'microsoft-edge'
        }
      ],
      win32: [
        {
          name: 'Google Chrome',
          type: 'chrome',
          executablePath: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
          command: 'chrome'
        },
        {
          name: 'Google Chrome (x86)',
          type: 'chrome',
          executablePath: 'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
          command: 'chrome'
        },
        {
          name: 'Microsoft Edge',
          type: 'edge',
          executablePath: 'C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe',
          command: 'msedge'
        }
      ],
      linux: [
        {
          name: 'Google Chrome',
          type: 'chrome',
          executablePath: '/usr/bin/google-chrome',
          command: 'google-chrome'
        },
        {
          name: 'Chromium',
          type: 'chromium',
          executablePath: '/usr/bin/chromium-browser',
          command: 'chromium-browser'
        },
        {
          name: 'Chromium (snap)',
          type: 'chromium',
          executablePath: '/snap/bin/chromium',
          command: 'chromium'
        }
      ]
    };

    return configs[platform] || configs.linux;
  }

  /**
   * 检查浏览器是否可用
   */
  async checkBrowserAvailability(config) {
    // 首先检查文件是否存在
    if (await fs.pathExists(config.executablePath)) {
      try {
        // 尝试获取版本信息
        const version = await this.getBrowserVersion(config);
        config.version = version;
        return true;
      } catch (error) {
        if (this.options.verbose) {
          console.log(chalk.yellow(`⚠️  ${config.name} 文件存在但无法执行: ${error.message}`));
        }
        return false;
      }
    }

    // 如果文件不存在，尝试通过命令检查
    try {
      await execAsync(`which ${config.command}`, { timeout: 5000 });
      const version = await this.getBrowserVersion(config);
      config.version = version;
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取浏览器版本
   */
  async getBrowserVersion(config) {
    try {
      const versionCommand = `"${config.executablePath}" --version`;
      const { stdout } = await execAsync(versionCommand, { timeout: 10000 });
      return stdout.trim();
    } catch (error) {
      return 'Unknown';
    }
  }

  /**
   * 选择最佳浏览器
   */
  selectBestBrowser() {
    if (this.detectedBrowsers.length === 0) {
      return null;
    }

    // 优先选择 Puppeteer 浏览器
    const puppeteerBrowser = this.detectedBrowsers.find(b => b.type === 'puppeteer');
    if (puppeteerBrowser) {
      this.selectedBrowser = puppeteerBrowser;
      return puppeteerBrowser;
    }

    // 按优先级选择系统浏览器
    for (const preferredType of this.options.preferredBrowsers) {
      const browser = this.detectedBrowsers.find(b => b.type === preferredType);
      if (browser) {
        this.selectedBrowser = browser;
        return browser;
      }
    }

    // 如果没有找到优先浏览器，选择第一个可用的
    this.selectedBrowser = this.detectedBrowsers[0];
    return this.selectedBrowser;
  }

  /**
   * 确保有可用的浏览器
   */
  async ensureBrowser() {
    await this.detectAvailableBrowsers();

    if (this.detectedBrowsers.length === 0) {
      console.log(chalk.yellow('⚠️  未找到可用浏览器，尝试下载 Chrome...'));
      const success = await this.downloadPuppeteerBrowser();

      if (success) {
        await this.detectPuppeteerBrowsers();
      } else {
        throw new Error('无法找到或下载可用的浏览器');
      }
    }

    const selectedBrowser = this.selectBestBrowser();
    if (!selectedBrowser) {
      throw new Error('无法选择可用的浏览器');
    }

    console.log(chalk.green(`✅ 选择浏览器: ${selectedBrowser.name}`));
    return selectedBrowser;
  }

  /**
   * 下载浏览器（优先尝试 Puppeteer，然后尝试 Playwright）
   */
  async downloadPuppeteerBrowser() {
    // 首先尝试下载 Puppeteer 浏览器
    const puppeteerSuccess = await this.downloadBrowserForAdapter('puppeteer');
    if (puppeteerSuccess) {
      return true;
    }

    // 如果 Puppeteer 失败，尝试 Playwright
    console.log(chalk.yellow('⚠️  Puppeteer 下载失败，尝试 Playwright...'));
    const playwrightSuccess = await this.downloadBrowserForAdapter('playwright-chromium');

    return playwrightSuccess;
  }

  /**
   * 为指定适配器下载浏览器
   */
  async downloadBrowserForAdapter(adapterType) {
    try {
      const adapter = browserFactory.createBrowserAutomation(adapterType);

      console.log(chalk.blue(`📥 正在下载 ${adapterType} 浏览器...`));

      const success = await adapter.downloadBrowser();

      if (success) {
        console.log(chalk.green(`✅ ${adapterType} 浏览器下载完成`));
        return true;
      } else {
        console.log(chalk.red(`❌ ${adapterType} 浏览器下载失败`));
        return false;
      }
    } catch (error) {
      console.log(chalk.red(`❌ ${adapterType} 浏览器下载失败: ${error.message}`));
      return false;
    }
  }

  /**
   * 打印检测到的浏览器
   */
  printDetectedBrowsers() {
    console.log(chalk.blue('\n🌐 检测到的浏览器:'));

    if (this.detectedBrowsers.length === 0) {
      console.log(chalk.yellow('   未找到可用浏览器'));
      return;
    }

    for (const browser of this.detectedBrowsers) {
      const status = browser.available ? '✅' : '❌';
      console.log(chalk.gray(`   ${status} ${browser.name} (${browser.version || 'Unknown'})`));
    }
  }
}

module.exports = BrowserDetector;
