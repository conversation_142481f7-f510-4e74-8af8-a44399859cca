const path = require('path');
const fs = require('fs-extra');
const DevRunFixer = require('../../src/domain/dev-run-fix/DevRunFixer');

describe('DevRunFixer Integration Test', () => {
  let tempDir;
  let devRunFixer;

  beforeEach(async () => {
    // 创建临时测试目录
    tempDir = path.join(__dirname, '../temp', `dev-run-fix-integration-${Date.now()}`);
    await fs.ensureDir(tempDir);

    // 创建基本的项目结构
    await fs.writeJson(path.join(tempDir, 'package.json'), {
      name: 'test-project',
      scripts: {
        dev: 'vue-cli-service serve'
      }
    });

    devRunFixer = new DevRunFixer(tempDir, {
      verbose: true,
      dryRun: true,
      timeout: 5000,
      maxAttempts: 2
    });
  });

  afterEach(async () => {
    // 清理临时目录
    if (tempDir && await fs.pathExists(tempDir)) {
      await fs.remove(tempDir);
    }
  });

  describe('ESLint 配置错误处理', () => {
    it('应该能够识别和处理 ESLint 配置错误', async () => {
      // 创建有问题的 .eslintrc.js 文件（模拟你提到的错误）
      const eslintrcContent = `
module.exports = {
  rules: {
    'vue/max-attributes-per-line': {
      max: 1,
      allowFirstLine: false
    }
  }
};
      `;
      
      await fs.writeFile(path.join(tempDir, '.eslintrc.js'), eslintrcContent);

      // 模拟你提到的错误输出
      const errorOutput = `
ERROR  Failed to compile with 1 error

[eslint] .eslintrc.js:
	Configuration for rule "vue/max-attributes-per-line" is invalid:
	Value {"max":1,"allowFirstLine":false} should be number.
	Value {"max":1,"allowFirstLine":false} should NOT have additional properties.
	Value {"max":1,"allowFirstLine":false} should match exactly one schema in oneOf.

You may use special comments to disable some warnings.
Use // eslint-disable-next-line to ignore the next line.
Use /* eslint-disable */ to ignore all warnings in a file.
ERROR in [eslint] .eslintrc.js:
	Configuration for rule "vue/max-attributes-per-line" is invalid:
	Value {"max":1,"allowFirstLine":false} should be number.
	Value {"max":1,"allowFirstLine":false} should NOT have additional properties.
	Value {"max":1,"allowFirstLine":false} should match exactly one schema in oneOf.
      `;

      // 测试错误类型识别
      const errorType = devRunFixer.identifyErrorType(errorOutput);
      expect(errorType).toBe('eslint-config');

      // 测试错误修复
      const result = await devRunFixer.fixEslintConfigError(errorOutput);
      
      // 验证结果
      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('filesModified');
      expect(result).toHaveProperty('errorsFixed');
      
      console.log('ESLint 配置错误修复结果:', result);
    });

    it('应该能够处理 Vue 编译器警告', async () => {
      const errorOutput = `
[@vue/compiler-sfc] ::v-deep usage as a combinator has been deprecated. Use :deep(<inner-selector>) instead of ::v-deep <inner-selector>.
      `;

      const errorType = devRunFixer.identifyErrorType(errorOutput);
      expect(errorType).toBe('vue-compiler');

      const result = await devRunFixer.fixVueCompilerError(errorOutput);
      expect(result.success).toBe(true);
      expect(result.message).toContain('警告已忽略');
    });
  });

  describe('完整的修复流程', () => {
    it('应该能够处理完整的开发服务器修复流程', async () => {
      // 这个测试模拟完整的修复流程
      const result = await devRunFixer.fixDevServer();
      
      // 验证返回的结果结构
      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('attempts');
      expect(result).toHaveProperty('stats');
      
      // 验证统计信息
      expect(result.stats).toHaveProperty('attempts');
      expect(result.stats).toHaveProperty('errorsFixed');
      expect(result.stats).toHaveProperty('filesModified');
      
      console.log('完整修复流程结果:', result);
    });
  });
});
