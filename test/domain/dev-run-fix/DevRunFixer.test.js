const path = require('path');
const fs = require('fs-extra');
const DevRunFixer = require('../../../src/domain/dev-run-fix/DevRunFixer');

describe('DevRunFixer', () => {
  let tempDir;
  let devRunFixer;

  beforeEach(async () => {
    // 创建临时测试目录
    tempDir = path.join(__dirname, '../../temp', `dev-run-fix-test-${Date.now()}`);
    await fs.ensureDir(tempDir);

    // 创建基本的项目结构
    await fs.writeJson(path.join(tempDir, 'package.json'), {
      name: 'test-project',
      scripts: {
        dev: 'vue-cli-service serve'
      }
    });

    devRunFixer = new DevRunFixer(tempDir, {
      verbose: false,
      dryRun: true,
      timeout: 5000, // 短超时用于测试
      maxAttempts: 2
    });
  });

  afterEach(async () => {
    // 清理临时目录
    if (tempDir && await fs.pathExists(tempDir)) {
      await fs.remove(tempDir);
    }
  });

  describe('错误类型识别', () => {
    it('应该正确识别 ESLint 配置错误', () => {
      const output = `
ERROR  Failed to compile with 1 error

[eslint] .eslintrc.js:
	Configuration for rule "vue/max-attributes-per-line" is invalid:
	Value {"max":1,"allowFirstLine":false} should be number.
      `;
      
      const errorType = devRunFixer.identifyErrorType(output);
      expect(errorType).toBe('eslint-config');
    });

    it('应该正确识别 Vue 编译器警告', () => {
      const output = `
[@vue/compiler-sfc] ::v-deep usage as a combinator has been deprecated. Use :deep(<inner-selector>) instead of ::v-deep <inner-selector>.
      `;
      
      const errorType = devRunFixer.identifyErrorType(output);
      expect(errorType).toBe('vue-compiler');
    });

    it('应该正确识别依赖缺失错误', () => {
      const output = `
Module not found: Error: Can't resolve 'some-missing-module' in '/path/to/project'
      `;
      
      const errorType = devRunFixer.identifyErrorType(output);
      expect(errorType).toBe('dependency-missing');
    });

    it('应该正确识别语法错误', () => {
      const output = `
SyntaxError: Unexpected token '}' in /path/to/file.js
      `;
      
      const errorType = devRunFixer.identifyErrorType(output);
      expect(errorType).toBe('syntax-error');
    });

    it('应该将未知错误归类为通用错误', () => {
      const output = `
Some unknown error occurred
      `;
      
      const errorType = devRunFixer.identifyErrorType(output);
      expect(errorType).toBe('generic');
    });
  });

  describe('开发服务器状态检测', () => {
    it('应该正确检测开发服务器启动成功', () => {
      const successOutputs = [
        'Local:   http://localhost:8080/',
        'App running at: http://localhost:8080/',
        'webpack compiled with 0 errors',
        'Compiled successfully in 1234ms',
        'ready - started server on http://localhost:3000'
      ];

      successOutputs.forEach(output => {
        expect(devRunFixer.isDevServerStarted(output)).toBe(true);
      });
    });

    it('应该正确检测致命错误', () => {
      const fatalOutputs = [
        'ERROR  Failed to compile with 1 error',
        'Configuration for rule "vue/max-attributes-per-line" is invalid',
        'Module not found: Error: Can\'t resolve \'module\'',
        'SyntaxError: Unexpected token',
        'TypeError: Cannot read property',
        'ReferenceError: variable is not defined'
      ];

      fatalOutputs.forEach(output => {
        expect(devRunFixer.isFatalError(output)).toBe(true);
      });
    });
  });

  describe('ESLint 配置错误修复', () => {
    it('应该能够处理 ESLint 配置错误', async () => {
      // 创建有问题的 .eslintrc.js 文件
      const eslintrcContent = `
module.exports = {
  rules: {
    'vue/max-attributes-per-line': {
      max: 1,
      allowFirstLine: false
    }
  }
};
      `;
      
      await fs.writeFile(path.join(tempDir, '.eslintrc.js'), eslintrcContent);

      const errorOutput = `
ERROR  Failed to compile with 1 error

[eslint] .eslintrc.js:
	Configuration for rule "vue/max-attributes-per-line" is invalid:
	Value {"max":1,"allowFirstLine":false} should be number.
      `;

      const result = await devRunFixer.fixEslintConfigError(errorOutput);
      
      // 在 dryRun 模式下，应该返回成功但不实际修改文件
      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('filesModified');
      expect(result).toHaveProperty('errorsFixed');
    });

    it('应该在 .eslintrc.js 文件不存在时返回错误', async () => {
      const errorOutput = `
Configuration for rule "vue/max-attributes-per-line" is invalid
      `;

      const result = await devRunFixer.fixEslintConfigError(errorOutput);

      expect(result.success).toBe(false);
      expect(result.error).toContain('.eslintrc.js 文件不存在');
    });
  });

  describe('Vue 编译器错误修复', () => {
    it('应该能够处理 Vue 编译器警告', async () => {
      const errorOutput = `
[@vue/compiler-sfc] ::v-deep usage as a combinator has been deprecated. Use :deep(<inner-selector>) instead of ::v-deep <inner-selector>.
      `;

      const result = await devRunFixer.fixVueCompilerError(errorOutput);
      
      // Vue 编译器警告通常不会阻止启动，所以应该返回成功
      expect(result.success).toBe(true);
      expect(result.message).toContain('警告已忽略');
    });
  });

  describe('统计信息', () => {
    it('应该正确跟踪修复统计信息', () => {
      const stats = devRunFixer.getStats();
      
      expect(stats).toHaveProperty('attempts');
      expect(stats).toHaveProperty('errorsFixed');
      expect(stats).toHaveProperty('filesModified');
      expect(stats).toHaveProperty('buildFixAgentStats');

      expect(stats.attempts).toBe(0);
      expect(stats.errorsFixed).toBe(0);
      expect(stats.filesModified).toBe(0);
    });
  });

  describe('集成测试', () => {
    it('应该能够处理完整的修复流程', async () => {
      // 这个测试需要模拟实际的开发服务器启动过程
      // 由于涉及到实际的进程启动，我们在这里只测试基本的流程
      
      const result = await devRunFixer.fixDevServer();
      
      // 在测试环境中，由于没有实际的 npm run dev 命令，应该会失败
      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('attempts');
      expect(result).toHaveProperty('stats');
    });
  });

  describe('错误处理', () => {
    it('应该在达到最大尝试次数后停止', async () => {
      const result = await devRunFixer.fixDevServer();
      
      expect(result.attempts).toBe(devRunFixer.options.maxAttempts);
    });

    it('应该正确处理无效的命令', async () => {
      devRunFixer.options.devCommand = 'invalid-command-that-does-not-exist';

      const result = await devRunFixer.tryStartDevServer();

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      // 可能是超时错误、命令不存在错误或异常退出错误
      expect(result.error).toMatch(/(超时|出错|失败|not found|command not found|异常退出)/i);
    });
  });
});
