const fs = require('fs-extra');
const path = require('path');
const { createUnifiedLogService } = require('../../../src/infrastructure/logging');

describe('UnifiedLogService', () => {
  let logService;
  let testProjectPath;
  let testLogDir;

  beforeEach(async () => {
    // 创建临时测试目录
    testProjectPath = path.join(__dirname, '../../fixtures/temp-log-test');
    testLogDir = path.join(testProjectPath, 'logs');
    
    await fs.ensureDir(testProjectPath);
    
    logService = createUnifiedLogService(testProjectPath, {
      enableDebugLog: false
    });
    
    await logService.initialize();
  });

  afterEach(async () => {
    // 清理测试目录
    await fs.remove(testProjectPath);
  });

  describe('初始化', () => {
    it('应该创建日志目录结构', async () => {
      expect(await fs.pathExists(testLogDir)).toBe(true);
      expect(await fs.pathExists(path.join(testLogDir, 'ai'))).toBe(true);
      expect(await fs.pathExists(path.join(testLogDir, 'migration'))).toBe(true);
      expect(await fs.pathExists(path.join(testLogDir, 'validation'))).toBe(true);
    });
  });

  describe('AI提示词日志', () => {
    it('应该以YAML格式记录AI提示词', async () => {
      const promptData = {
        prompt: '请帮我修复这个Vue组件的语法错误',
        context: {
          file: 'test.vue',
          error: 'Syntax error'
        }
      };

      const success = await logService.logAIPrompt(promptData, {
        taskType: 'vue-fix',
        phase: 'syntax-check',
        attemptNumber: 1
      });

      expect(success).toBe(true);

      // 检查文件是否存在且为YAML格式
      const files = await fs.readdir(testLogDir);
      const yamlFiles = files.filter(f => f.endsWith('.yml') && f.includes('ai-prompt'));
      expect(yamlFiles.length).toBeGreaterThan(0);

      // 检查文件内容
      const yamlContent = await fs.readFile(path.join(testLogDir, yamlFiles[0]), 'utf8');
      expect(yamlContent).toContain('prompt: |');
      expect(yamlContent).toContain('请帮我修复这个Vue组件的语法错误');
    });
  });

  describe('迁移错误日志', () => {
    it('应该以Markdown格式记录迁移错误', async () => {
      const errorData = {
        file: 'test.vue',
        error: 'Transform failed',
        stack: 'Error stack trace...',
        context: {
          fileType: '.vue',
          fileSize: 1024
        }
      };

      const success = await logService.logMigrationError(errorData, {
        taskType: 'vue-migration',
        phase: 'transform'
      });

      expect(success).toBe(true);

      // 检查文件是否存在且为Markdown格式
      const files = await fs.readdir(testLogDir);
      const mdFiles = files.filter(f => f.endsWith('.md') && f.includes('migration-error'));
      expect(mdFiles.length).toBeGreaterThan(0);

      // 检查文件内容
      const mdContent = await fs.readFile(path.join(testLogDir, mdFiles[0]), 'utf8');
      expect(mdContent).toContain('# 迁移错误报告');
      expect(mdContent).toContain('**文件**: test.vue');
      expect(mdContent).toContain('**错误**: Transform failed');
    });
  });

  describe('验证报告', () => {
    it('应该以Markdown格式记录验证报告', async () => {
      const reportData = {
        summary: {
          total: 10,
          successful: 8,
          failed: 2,
          successRate: 80
        },
        failedPages: [
          {
            url: '/test1',
            errors: ['404 Not Found']
          },
          {
            url: '/test2',
            errors: ['Timeout']
          }
        ]
      };

      const success = await logService.logValidationReport(reportData, {
        taskType: 'page-validation',
        phase: 'final-check'
      });

      expect(success).toBe(true);

      // 检查文件是否存在且为Markdown格式
      const files = await fs.readdir(testLogDir);
      const mdFiles = files.filter(f => f.endsWith('.md') && f.includes('validation-report'));
      expect(mdFiles.length).toBeGreaterThan(0);

      // 检查文件内容
      const mdContent = await fs.readFile(path.join(testLogDir, mdFiles[0]), 'utf8');
      expect(mdContent).toContain('# 验证报告');
      expect(mdContent).toContain('- 总页面数: 10');
      expect(mdContent).toContain('- 成功率: 80%');
    });
  });

  describe('会话管理', () => {
    it('应该正确管理会话生命周期', async () => {
      // 开始会话
      const session = logService.startSession({
        type: 'test-migration',
        description: '测试迁移会话'
      });

      expect(session).not.toBeNull();
      expect(typeof session.sessionId).toBe('string');

      // 记录一些活动
      await logService.logAIPrompt({
        prompt: '测试提示词'
      });

      await logService.logMigrationSuccess({
        file: 'test.vue',
        transformInfo: { rules: ['vue3-syntax'] }
      });

      // 获取会话状态
      const status = logService.getSessionStatus();
      expect(status.isActive).toBe(true);
      expect(status.sessionId).toBe(session.sessionId);

      // 结束会话
      await logService.endSession({
        success: true,
        summary: { filesProcessed: 1 }
      });

      const finalStatus = logService.getSessionStatus();
      expect(finalStatus.isActive).toBe(false);
    });
  });

  describe('综合报告', () => {
    it('应该生成综合报告', async () => {
      // 记录一些活动
      await logService.logAIPrompt({
        prompt: '测试提示词1'
      });

      await logService.logMigrationError({
        file: 'error.vue',
        error: '测试错误'
      });

      await logService.logMigrationSuccess({
        file: 'success.vue',
        transformInfo: { rules: ['vue3-syntax'] }
      });

      // 生成综合报告
      const { report, filePath } = await logService.generateComprehensiveReport();

      expect(typeof report).toBe('object');
      expect(typeof report.generatedAt).toBe('string');
      expect(typeof report.logDirectory).toBe('object');
      expect(typeof filePath).toBe('string');

      // 检查报告文件是否存在
      expect(await fs.pathExists(filePath)).toBe(true);

      // 检查是否为Markdown格式
      expect(path.extname(filePath)).toBe('.md');
    });
  });

  describe('日志搜索', () => {
    it('应该能够搜索日志内容', async () => {
      // 记录一些测试数据
      await logService.logAIPrompt({
        prompt: '搜索测试关键词'
      });

      await logService.logMigrationError({
        file: 'search-test.vue',
        error: '包含搜索测试关键词的错误'
      });

      // 等待文件写入完成
      await new Promise(resolve => setTimeout(resolve, 100));

      // 搜索日志
      const results = await logService.searchLogs('搜索测试关键词', {
        limit: 10
      });

      expect(Array.isArray(results)).toBe(true);
      // 注意：搜索可能需要时间，结果可能为空，这取决于实现
    });
  });
});
