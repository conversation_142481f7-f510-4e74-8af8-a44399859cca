# Vue 2 到 Vue 3 自动迁移工具

一个智能化的 Vue 2 到 Vue 3 项目迁移工具，集成了 AI 辅助修复功能，帮助您快速、安全地完成项目升级。

## 🚀 快速开始

### 1. 环境配置

首先，复制 `.env.example` 文件为 `.env` 并配置 AI 服务：

```bash
cp .env.example .env
```

#### AI 服务配置

本工具集成了智能 AI 修复功能，用于自动分析和修复迁移过程中遇到的复杂错误。AI 服务主要用于：

- **构建错误分析**：智能识别 Vue 3 兼容性问题并提供修复建议
- **代码转换优化**：分析复杂的语法转换场景
- **依赖冲突解决**：自动处理包版本冲突和兼容性问题
- **运行时错误修复**：分析页面运行时错误并生成修复代码

在 `.env` 文件中配置以下参数：

```env
# 银河 AI 模型平台
# 当这四个环境变量都存在时，AiService 将优先使用 Galaxy AI
GALAXY_SYSTEM_ID=your-system-id
GALAXY_SYSTEM_SECRET=your-system-secret
GALAXY_APP_ID=your-app-id
GALAXY_ACCOUNT=your-account
GALAXY_BASE_URL=http://copilot.prodgpu.chinastock.com.cn

# 外部 AI 服务 (可选)
# GLM_TOKEN=your-glm-token
# DEEPSEEK_TOKEN=your-deepseek-token
```

> **注意**：AI 服务配置是可选的，但强烈推荐配置以获得最佳的自动修复体验。

### 2. 安装依赖

```bash
npm install
# 或
pnpm install
```

## 📋 使用方式

### 方式一：全自动迁移（推荐）

使用 `auto-migrator.js` 一键完成完整的迁移流程：

```bash
# 就地迁移（直接在原项目上修改）
node bin/auto-migrator.js migrate /path/to/your/vue2-project

# 预览模式（不实际修改文件）
node bin/auto-migrator.js migrate /path/to/your/project --dry-run

# 跳过特定步骤
node bin/auto-migrator.js migrate /path/to/your/project --skip-steps sass,validate

# 自定义配置
node bin/auto-migrator.js migrate /path/to/your/project \
  --build-command "npm run build" \
  --dev-command "npm run serve" \
  --username admin \
  --password 123456
```

#### 全自动迁移流程

```mermaid
graph TD
    A[开始迁移] --> B[1. 复制 vue.config.js]
    B --> C[2. 更新 package.json 依赖]
    C --> D[3. 迁移 Sass/SCSS 文件]
    D --> E[4. 迁移 Vue 代码文件]
    E --> F[5. 安装依赖包]
    F --> G[6. 修复构建错误]
    G --> H{构建成功?}
    H -->|是| I[7. 启动开发服务器]
    H -->|否| J[输出构建错误报告]
    I --> K{服务器启动成功?}
    K -->|是| L[8. 验证页面运行]
    K -->|否| M[输出服务器错误报告]
    L --> N[生成迁移报告]
    J --> N
    M --> N
    N --> O[迁移完成]
```

### 方式二：分步骤迁移

如果您希望更精细地控制迁移过程，可以使用单独的工具：

#### 1. Vue 代码迁移
```bash
# 使用 gogocode 迁移 Vue + Element UI 代码
node bin/vue-migrator.js migrate /path/to/your/project
```

#### 2. Sass 样式转换
```bash
# 转换 Element UI 到 Element Plus 的 Sass 变量
node bin/sass-migrator.js migrate /path/to/your/project
```

#### 3. 构建错误修复
```bash
# 自动修复构建时的错误
node bin/build-fixer.js fix /path/to/your/project

# 使用自定义构建命令
node bin/build-fixer.js fix /path/to/your/project --build-command "npm run build:prod"

# 快速检测模式（30秒超时）
node bin/build-fixer.js fix /path/to/your/project --mode dev
```

#### 4. 开发服务器修复
```bash
# 修复 npm run dev 启动错误
node bin/dev-run-fixer.js fix /path/to/your/project

# 自定义开发命令
node bin/dev-run-fixer.js fix /path/to/your/project --dev-command "npm run serve"
```

#### 5. 页面运行时验证
```bash
# 验证页面运行状态
node bin/page-validator.js check /path/to/your/project

# 指定服务器地址和登录信息
node bin/page-validator.js check /path/to/your/project \
  --base-url http://localhost:8080 \
  --username admin \
  --password 123456

# 测试特定路由
node bin/page-validator.js check /path/to/your/project \
  --routes "/dashboard,/user/profile"
```

## 🔧 高级配置

### 命令行选项

#### auto-migrator.js 选项
- `--verbose`: 显示详细信息
- `--dry-run`: 预览模式，不实际修改文件
- `--skip-steps <steps>`: 跳过指定步骤 (config,package,sass,vue,install,build,devserver,validate)
- `--build-command <cmd>`: 自定义构建命令 (默认: pnpm run build:prod)
- `--dev-command <cmd>`: 自定义开发命令 (默认: pnpm run dev)
- `--install-command <cmd>`: 自定义安装命令 (默认: pnpm install)
- `--max-build-attempts <num>`: 最大构建修复尝试次数 (默认: 6)
- `--dev-server-port <port>`: 开发服务器端口 (默认: 9527)
- `--username <username>`: 页面验证登录用户名 (默认: admin)
- `--password <password>`: 页面验证登录密码 (默认: 111111)

### 项目结构要求

确保您的 Vue 2 项目具有以下基本结构：
```
your-vue2-project/
├── package.json          # 必需
├── src/                  # 源代码目录
│   ├── main.js          # 入口文件
│   ├── App.vue          # 根组件
│   └── ...
├── public/              # 静态资源
└── ...
```

## ⚠️ 注意事项

1. **备份项目**：建议在迁移前备份您的项目
2. **测试环境**：建议先在测试环境中验证迁移结果
3. **依赖冲突**：某些第三方库可能需要手动升级
4. **自定义组件**：复杂的自定义组件可能需要手动调整

## 🤝 支持

如果在使用过程中遇到问题，请：
1. 查看生成的错误报告
2. 使用 `--verbose` 选项获取详细信息
3. 检查 AI 服务配置是否正确

---

**祝您迁移顺利！** 🎉
