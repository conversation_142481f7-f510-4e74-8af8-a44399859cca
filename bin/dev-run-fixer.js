#!/usr/bin/env node

require('dotenv').config();

const { Command } = require('commander');
const chalk = require('chalk');
const path = require('path');
const fs = require('fs-extra');
const ora = require('ora');
const DevRunFixer = require('../src/domain/dev-run-fix/DevRunFixer');

const program = new Command();

program
  .name('dev-run-fixer')
  .description('Vue 项目开发服务器启动错误自动修复工具')
  .version('1.0.0');

program
  .command('fix [project-path]')
  .description('🔧 修复开发服务器启动错误')
  .option('-c, --dev-command <cmd>', '开发服务器命令', 'npm run dev')
  .option('-t, --timeout <ms>', '启动超时时间(毫秒)', '60000')
  .option('-m, --max-attempts <num>', '最大修复尝试次数', '3')
  .option('--dry-run', '预览模式，不实际修改文件')
  .option('--verbose', '显示详细信息')
  .action(async (projectPath, options) => {
    const spinner = ora('初始化开发服务器修复器...').start();

    try {
      // 确定项目路径
      const targetPath = projectPath ? path.resolve(projectPath) : process.cwd();

      // 验证项目路径
      if (!await fs.pathExists(targetPath)) {
        throw new Error(`项目路径不存在: ${targetPath}`);
      }

      // 验证是否为有效的 Node.js 项目
      const packageJsonPath = path.join(targetPath, 'package.json');
      if (!await fs.pathExists(packageJsonPath)) {
        throw new Error(`未找到 package.json 文件: ${packageJsonPath}`);
      }

      spinner.succeed('项目验证通过');

      // 设置环境变量以控制详细输出
      if (options.verbose) {
        process.env.VERBOSE = 'true';
      }

      const fixerOptions = {
        devCommand: options.devCommand,
        timeout: parseInt(options.timeout),
        maxAttempts: parseInt(options.maxAttempts),
        dryRun: options.dryRun || false,
        verbose: options.verbose || false
      };

      console.log(chalk.blue('\n🔧 开始修复开发服务器启动问题...'));
      console.log(chalk.gray(`📁 项目路径: ${targetPath}`));
      console.log(chalk.gray(`🚀 开发命令: ${fixerOptions.devCommand}`));
      console.log(chalk.gray(`⏱️  超时时间: ${fixerOptions.timeout / 1000}s`));
      console.log(chalk.gray(`🔄 最大尝试次数: ${fixerOptions.maxAttempts}`));

      if (fixerOptions.dryRun) {
        console.log(chalk.yellow('🔍 运行在预览模式，不会修改文件'));
      }

      const devRunFixer = new DevRunFixer(targetPath, fixerOptions);
      const startTime = Date.now();
      const result = await devRunFixer.fixDevServer();
      const endTime = Date.now();

      displayResults(result, fixerOptions, endTime - startTime);
    } catch (error) {
      spinner.fail('开发服务器修复失败');
      console.error(chalk.red('\n❌ 错误:'), error.message);

      if (options.verbose) {
        console.error(chalk.gray('\n详细错误信息:'));
        console.error(chalk.gray(error.stack));
      }

      process.exit(1);
    }
  });

function displayResults(result, _options, duration) {
  console.log(chalk.blue('\n📊 修复结果总览:'));

  // 状态显示
  const statusIcon = result.success ? '🎉' : '⚠️';
  const statusColor = result.success ? chalk.green : chalk.yellow;
  console.log(`${statusIcon} 状态: ${statusColor(result.success ? '修复成功' : '修复失败')}`);

  // 统计信息
  console.log(chalk.gray('┌─ 统计信息'));
  console.log(chalk.gray(`├─ 修复尝试: ${result.attempts} 次`));
  console.log(chalk.gray(`├─ 修复错误: ${chalk.green(result.stats?.errorsFixed || 0)} 个`));
  console.log(chalk.gray(`├─ 修改文件: ${chalk.green(result.stats?.filesModified || 0)} 个`));

  if (duration) {
    const durationSeconds = Math.round(duration / 1000);
    console.log(chalk.gray(`└─ 总耗时: ${durationSeconds}s`));
  }

  // BuildFixAgent 统计信息
  const buildFixStats = result.stats?.buildFixAgentStats;
  if (buildFixStats) {
    console.log(chalk.blue('\n🔧 BuildFixAgent 统计:'));
    console.log(chalk.gray(`├─ AI 分析次数: ${buildFixStats.aiAnalysisCount || 0}`));
    console.log(chalk.gray(`├─ 文件修复次数: ${buildFixStats.fileFixCount || 0}`));
    console.log(chalk.gray(`└─ 成功修复率: ${buildFixStats.successRate || 0}%`));
  }

  // 详细原因
  if (result.error) {
    console.log(chalk.gray(`\n📝 详细信息: ${result.error}`));
  }

  // 成功情况的建议
  if (result.success) {
    console.log(chalk.green('\n🎉 恭喜！开发服务器启动成功！'));
    console.log(chalk.blue('\n📋 后续建议:'));
    console.log(chalk.gray('  ✓ 检查浏览器中的应用是否正常运行'));
    console.log(chalk.gray('  ✓ 验证修复后的代码是否符合预期'));
    console.log(chalk.gray('  ✓ 提交代码变更'));
    console.log(chalk.gray('  ✓ 更新项目文档（如有必要）'));
  } else {
    // 失败情况的详细建议
    console.log(chalk.yellow('\n⚠️  开发服务器启动失败'));
    console.log(chalk.blue('\n🔧 下一步操作建议:'));
    console.log(chalk.gray('  1. 查看详细错误信息'));
    console.log(chalk.gray('  2. 尝试手动修复复杂的错误'));
    console.log(chalk.gray('  3. 使用 --verbose 查看详细日志'));
    console.log(chalk.gray('  4. 检查 ESLint 配置是否正确'));
    console.log(chalk.gray('  5. 确保所有依赖都已正确安装'));

    console.log(chalk.blue('\n💡 常见解决方案:'));
    console.log(chalk.gray('  • 检查 .eslintrc.js 配置文件'));
    console.log(chalk.gray('  • 运行 npm install 重新安装依赖'));
    console.log(chalk.gray('  • 检查 Vue 3 兼容性问题'));
    console.log(chalk.gray('  • 参考 Vue 3 迁移指南'));
  }

  console.log(chalk.blue('\n❓ 需要帮助?'));
  console.log(chalk.gray('  • 运行 dev-run-fixer --help 查看所有选项'));
  console.log(chalk.gray('  • 使用 test 命令仅测试启动（不修复）'));
  console.log(chalk.gray('  • 查看项目文档获取更多信息'));
}

process.on('uncaughtException', (error) => {
  console.error(chalk.red('❌ 未捕获的异常:'), error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason, _promise) => {
  console.error(chalk.red('❌ 未处理的 Promise 拒绝:'), reason);
  process.exit(1);
});

program.parse();

if (!process.argv.slice(2).length) {
  program.outputHelp();
}
